#ifndef MQTT_MANAGER_H
#define MQTT_MANAGER_H

#include <Arduino.h>
#include <ESP8266WiFi.h>
#include <PubSubClient.h>
#include <EEPROM.h>
#include "DeviceManager.h"

// EEPROM layout for MQTT settings
#define MQTT_BROKER_ADDR 200
#define MQTT_BROKER_SIZE 64
#define MQTT_PORT_ADDR (MQTT_BROKER_ADDR + MQTT_BROKER_SIZE)
#define MQTT_PORT_SIZE 2
#define MQTT_USERNAME_ADDR (MQTT_PORT_ADDR + MQTT_PORT_SIZE)
#define MQTT_USERNAME_SIZE 32
#define MQTT_PASSWORD_ADDR (MQTT_USERNAME_ADDR + MQTT_USERNAME_SIZE)
#define MQTT_PASSWORD_SIZE 32
#define MQTT_ENABLED_ADDR (MQTT_PASSWORD_ADDR + MQTT_PASSWORD_SIZE)

// Default MQTT settings
#define DEFAULT_MQTT_BROKER "192.168.1.100"
#define DEFAULT_MQTT_PORT 1883
#define DEFAULT_MQTT_USERNAME ""
#define DEFAULT_MQTT_PASSWORD ""

// MQTT topics
#define TOPIC_PREFIX "home/switches/"
#define TOPIC_SUFFIX_SET "/set"
#define TOPIC_SUFFIX_STATE "/state"
#define TOPIC_SUFFIX_AVAILABLE "/available"
#define TOPIC_SUFFIX_NAME "/name"
#define TOPIC_SUFFIX_NAME_SET "/name/set"
#define TOPIC_SUFFIX_RGB_OFF_SET "/rgb/off/set"
#define TOPIC_SUFFIX_RGB_ON_SET "/rgb/on/set"
#define TOPIC_SUFFIX_RGB_OFF_STATE "/rgb/off/state"
#define TOPIC_SUFFIX_RGB_ON_STATE "/rgb/on/state"
#define TOPIC_ALL "home/switches/all"

class MQTTManager
{
private:
    WiFiClient _wifiClient;
    PubSubClient _mqttClient;
    DeviceManager *_deviceManager;
    String _broker;
    int _port;
    String _username;
    String _password;
    bool _enabled;
    unsigned long _lastConnectionAttempt;
    unsigned long _connectionRetryInterval;
    bool _isConnected;

    // Read string from EEPROM at specified address
    String readStringFromEEPROM(int startAddr, int maxLength)
    {
        char data[maxLength + 1];
        for (int i = 0; i < maxLength; i++)
        {
            data[i] = char(EEPROM.read(startAddr + i));
        }
        data[maxLength] = '\0';
        return String(data);
    }

    // Write string to EEPROM at specified address
    void writeStringToEEPROM(int startAddr, const String &strToWrite, int maxLength)
    {
        for (int i = 0; i < maxLength; i++)
        {
            if (i < strToWrite.length())
            {
                EEPROM.write(startAddr + i, strToWrite[i]);
            }
            else
            {
                EEPROM.write(startAddr + i, 0);
            }
        }
        EEPROM.commit();
    }

    // Load MQTT settings from EEPROM
    void loadFromEEPROM()
    {
        // Check if MQTT settings have been initialized
        if (EEPROM.read(MQTT_ENABLED_ADDR) == 0xFF)
        {
            // First time initialization - save defaults
            _broker = DEFAULT_MQTT_BROKER;
            _port = DEFAULT_MQTT_PORT;
            _username = DEFAULT_MQTT_USERNAME;
            _password = DEFAULT_MQTT_PASSWORD;
            _enabled = false; // Disabled by default

            saveToEEPROM();
            Serial.println("4-Relay MQTT settings initialized with defaults");
        }
        else
        {
            // Load existing settings
            _broker = readStringFromEEPROM(MQTT_BROKER_ADDR, MQTT_BROKER_SIZE);
            _username = readStringFromEEPROM(MQTT_USERNAME_ADDR, MQTT_USERNAME_SIZE);
            _password = readStringFromEEPROM(MQTT_PASSWORD_ADDR, MQTT_PASSWORD_SIZE);
            _enabled = EEPROM.read(MQTT_ENABLED_ADDR) == 1;

            // Load port (stored as 2 bytes)
            _port = EEPROM.read(MQTT_PORT_ADDR) | (EEPROM.read(MQTT_PORT_ADDR + 1) << 8);

            Serial.println("4-Relay MQTT settings loaded from EEPROM");
        }

        Serial.print("4-Relay MQTT Broker: ");
        Serial.println(_broker);
        Serial.print("4-Relay MQTT Port: ");
        Serial.println(_port);
        Serial.print("4-Relay MQTT Enabled: ");
        Serial.println(_enabled ? "Yes" : "No");
    }

    // Save MQTT settings to EEPROM
    void saveToEEPROM()
    {
        writeStringToEEPROM(MQTT_BROKER_ADDR, _broker, MQTT_BROKER_SIZE);
        writeStringToEEPROM(MQTT_USERNAME_ADDR, _username, MQTT_USERNAME_SIZE);
        writeStringToEEPROM(MQTT_PASSWORD_ADDR, _password, MQTT_PASSWORD_SIZE);
        EEPROM.write(MQTT_ENABLED_ADDR, _enabled ? 1 : 0);

        // Save port (as 2 bytes)
        EEPROM.write(MQTT_PORT_ADDR, _port & 0xFF);
        EEPROM.write(MQTT_PORT_ADDR + 1, (_port >> 8) & 0xFF);

        EEPROM.commit();
        Serial.println("4-Relay MQTT settings saved to EEPROM");
    }

public:
    MQTTManager(DeviceManager *deviceManager)
        : _mqttClient(_wifiClient), _deviceManager(deviceManager), _lastConnectionAttempt(0),
          _connectionRetryInterval(30000), _isConnected(false) // 30 seconds retry interval
    {
    }

    // Initialize MQTT manager
    void begin()
    {
        // Load settings from EEPROM
        loadFromEEPROM();

        if (_enabled)
        {
            // Set MQTT server
            _mqttClient.setServer(_broker.c_str(), _port);

            Serial.println("4-Relay MQTT Manager initialized");
        }
        else
        {
            Serial.println("4-Relay MQTT is disabled");
        }
    }

    // Set callback function for incoming messages
    void setCallback(void (*callback)(char *, byte *, unsigned int))
    {
        _mqttClient.setCallback(callback);
    }

    // Connect to MQTT broker
    bool connect()
    {
        if (!_enabled)
        {
            return false;
        }

        if (_mqttClient.connected())
        {
            return true;
        }

        // Don't try to reconnect too frequently
        unsigned long now = millis();
        if (now - _lastConnectionAttempt < _connectionRetryInterval)
        {
            return false;
        }
        _lastConnectionAttempt = now;

        Serial.println("4-Relay attempting MQTT connection...");

        // Create a client ID based on the device ID
        String clientId = "ESP8266_4Relay_" + _deviceManager->getDeviceID();

        bool connected = false;
        if (_username.length() > 0)
        {
            connected = _mqttClient.connect(clientId.c_str(), _username.c_str(), _password.c_str());
        }
        else
        {
            connected = _mqttClient.connect(clientId.c_str());
        }

        if (connected)
        {
            _isConnected = true;
            Serial.println("4-Relay MQTT connected");

            // Subscribe to control topics
            subscribeToTopics();

            // Publish device availability
            publishAvailability(true);

            // Publish all current states
            publishAllStates();

            return true;
        }
        else
        {
            _isConnected = false;
            Serial.print("4-Relay MQTT connection failed, rc=");
            Serial.println(_mqttClient.state());
            return false;
        }
    }

    // Subscribe to control topics
    void subscribeToTopics()
    {
        if (!_mqttClient.connected())
        {
            return;
        }

        String deviceId = _deviceManager->getDeviceID();

        // Subscribe to individual switch control topics
        for (uint8_t i = 0; i < _deviceManager->getSwitchCount(); i++)
        {
            String topic = String(TOPIC_PREFIX) + deviceId + "/switch/" + i + TOPIC_SUFFIX_SET;
            _mqttClient.subscribe(topic.c_str());

            // Subscribe to RGB control topics
            String rgbOffTopic = String(TOPIC_PREFIX) + deviceId + "/switch/" + i + TOPIC_SUFFIX_RGB_OFF_SET;
            String rgbOnTopic = String(TOPIC_PREFIX) + deviceId + "/switch/" + i + TOPIC_SUFFIX_RGB_ON_SET;
            _mqttClient.subscribe(rgbOffTopic.c_str());
            _mqttClient.subscribe(rgbOnTopic.c_str());
        }

        // Subscribe to device name control topic
        String nameTopic = String(TOPIC_PREFIX) + deviceId + TOPIC_SUFFIX_NAME_SET;
        _mqttClient.subscribe(nameTopic.c_str());

        Serial.println("4-Relay subscribed to MQTT topics");
    }

    // Publish device availability
    void publishAvailability(bool available)
    {
        if (!_mqttClient.connected())
        {
            return;
        }

        String topic = String(TOPIC_PREFIX) + _deviceManager->getDeviceID() + TOPIC_SUFFIX_AVAILABLE;
        String payload = available ? "online" : "offline";

        publish(topic.c_str(), payload.c_str(), true);
    }

    // Publish all switch states
    void publishAllStates()
    {
        for (uint8_t i = 0; i < _deviceManager->getSwitchCount(); i++)
        {
            publishSwitchState(i);
            publishRGBOffState(i);
            publishRGBOnState(i);
        }

        // Also publish device name
        publishDeviceName();
    }

    // Publish device name
    void publishDeviceName()
    {
        if (!_mqttClient.connected())
        {
            return;
        }

        String topic = String(TOPIC_PREFIX) + _deviceManager->getDeviceID() + TOPIC_SUFFIX_NAME;
        String payload = _deviceManager->getDeviceName();

        publish(topic.c_str(), payload.c_str(), true);
    }

    // Publish a specific switch state
    void publishSwitchState(uint8_t switchIndex)
    {
        if (switchIndex >= _deviceManager->getSwitchCount())
        {
            return;
        }

        String topic = String(TOPIC_PREFIX) + _deviceManager->getDeviceID() + "/switch/" + switchIndex + TOPIC_SUFFIX_STATE;
        String payload = _deviceManager->getSwitchState(switchIndex) ? "ON" : "OFF";

        publish(topic.c_str(), payload.c_str(), true);
    }

    // Publish RGB OFF state
    void publishRGBOffState(uint8_t switchIndex)
    {
        if (switchIndex >= _deviceManager->getSwitchCount())
        {
            return;
        }

        bool r, g, b;
        _deviceManager->getRGBOff(switchIndex, r, g, b);

        String topic = String(TOPIC_PREFIX) + _deviceManager->getDeviceID() + "/switch/" + switchIndex + TOPIC_SUFFIX_RGB_OFF_STATE;
        String payload = "{\"r\":" + String(r ? 1 : 0) + ",\"g\":" + String(g ? 1 : 0) + ",\"b\":" + String(b ? 1 : 0) + "}";

        publish(topic.c_str(), payload.c_str(), true);
    }

    // Publish RGB ON state
    void publishRGBOnState(uint8_t switchIndex)
    {
        if (switchIndex >= _deviceManager->getSwitchCount())
        {
            return;
        }

        bool r, g, b;
        _deviceManager->getRGBOn(switchIndex, r, g, b);

        String topic = String(TOPIC_PREFIX) + _deviceManager->getDeviceID() + "/switch/" + switchIndex + TOPIC_SUFFIX_RGB_ON_STATE;
        String payload = "{\"r\":" + String(r ? 1 : 0) + ",\"g\":" + String(g ? 1 : 0) + ",\"b\":" + String(b ? 1 : 0) + "}";

        publish(topic.c_str(), payload.c_str(), true);
    }

    // Publish a message to MQTT broker
    bool publish(const char *topic, const char *payload, bool retained = false)
    {
        if (!_mqttClient.connected())
        {
            return false;
        }

        bool result = _mqttClient.publish(topic, payload, retained);
        if (result)
        {
            Serial.print("4-Relay MQTT published: ");
            Serial.print(topic);
            Serial.print(" = ");
            Serial.println(payload);
        }
        else
        {
            Serial.print("4-Relay MQTT publish failed: ");
            Serial.println(topic);
        }
        return result;
    }

    // Handle incoming MQTT messages
    void handleMessage(char *topic, byte *payload, unsigned int length)
    {
        // Convert payload to string
        String messageStr = "";
        for (unsigned int i = 0; i < length; i++)
        {
            messageStr += (char)payload[i];
        }

        String topicStr = String(topic);

        Serial.print("4-Relay MQTT message received: ");
        Serial.print(topicStr);
        Serial.print(" = ");
        Serial.println(messageStr);

        // Check if it's a switch control message for a specific switch
        String prefix = String(TOPIC_PREFIX) + _deviceManager->getDeviceID() + "/switch/";

        if (topicStr.startsWith(prefix) && topicStr.endsWith(TOPIC_SUFFIX_SET))
        {
            // Extract switch index
            int prefixLength = prefix.length();
            int suffixLength = String(TOPIC_SUFFIX_SET).length();
            String switchIndexStr = topicStr.substring(prefixLength, topicStr.length() - suffixLength);

            uint8_t switchIndex = switchIndexStr.toInt();

            // Set switch state
            if (messageStr == "ON")
            {
                _deviceManager->setSwitchState(switchIndex, true);
                publishSwitchState(switchIndex);
            }
            else if (messageStr == "OFF")
            {
                _deviceManager->setSwitchState(switchIndex, false);
                publishSwitchState(switchIndex);
            }
            else if (messageStr == "TOGGLE")
            {
                _deviceManager->toggleSwitch(switchIndex);
                publishSwitchState(switchIndex);
            }
        }

        // Check if it's an RGB OFF control message for a specific switch
        if (topicStr.startsWith(prefix) && topicStr.endsWith(TOPIC_SUFFIX_RGB_OFF_SET))
        {
            // Extract switch index
            int prefixLength = prefix.length();
            int suffixLength = String(TOPIC_SUFFIX_RGB_OFF_SET).length();
            String switchIndexStr = topicStr.substring(prefixLength, topicStr.length() - suffixLength);

            uint8_t switchIndex = switchIndexStr.toInt();

            // Parse JSON RGB message: {"r":1,"g":0,"b":0} (1=ON, 0=OFF)
            if (messageStr.startsWith("{") && messageStr.endsWith("}"))
            {
                // Simple JSON parsing for RGB values
                int rIndex = messageStr.indexOf("\"r\":");
                int gIndex = messageStr.indexOf("\"g\":");
                int bIndex = messageStr.indexOf("\"b\":");

                if (rIndex != -1 && gIndex != -1 && bIndex != -1)
                {
                    bool r = messageStr.substring(rIndex + 4, messageStr.indexOf(",", rIndex)).toInt() > 0;
                    bool g = messageStr.substring(gIndex + 4, messageStr.indexOf(",", gIndex)).toInt() > 0;
                    bool b = messageStr.substring(bIndex + 4, messageStr.indexOf("}", bIndex)).toInt() > 0;

                    _deviceManager->setRGBOff(switchIndex, r, g, b);
                    publishRGBOffState(switchIndex);
                }
            }
        }

        // Check if it's an RGB ON control message for a specific switch
        if (topicStr.startsWith(prefix) && topicStr.endsWith(TOPIC_SUFFIX_RGB_ON_SET))
        {
            // Extract switch index
            int prefixLength = prefix.length();
            int suffixLength = String(TOPIC_SUFFIX_RGB_ON_SET).length();
            String switchIndexStr = topicStr.substring(prefixLength, topicStr.length() - suffixLength);

            uint8_t switchIndex = switchIndexStr.toInt();

            // Parse JSON RGB message: {"r":0,"g":1,"b":0} (1=ON, 0=OFF)
            if (messageStr.startsWith("{") && messageStr.endsWith("}"))
            {
                // Simple JSON parsing for RGB values
                int rIndex = messageStr.indexOf("\"r\":");
                int gIndex = messageStr.indexOf("\"g\":");
                int bIndex = messageStr.indexOf("\"b\":");

                if (rIndex != -1 && gIndex != -1 && bIndex != -1)
                {
                    bool r = messageStr.substring(rIndex + 4, messageStr.indexOf(",", rIndex)).toInt() > 0;
                    bool g = messageStr.substring(gIndex + 4, messageStr.indexOf(",", gIndex)).toInt() > 0;
                    bool b = messageStr.substring(bIndex + 4, messageStr.indexOf("}", bIndex)).toInt() > 0;

                    _deviceManager->setRGBOn(switchIndex, r, g, b);
                    publishRGBOnState(switchIndex);
                }
            }
        }

        // Check if it's a device name control message
        String namePrefix = String(TOPIC_PREFIX) + _deviceManager->getDeviceID() + TOPIC_SUFFIX_NAME_SET;
        if (topicStr == namePrefix)
        {
            _deviceManager->setDeviceName(messageStr);
            publishDeviceName();
        }
    }

    // Main loop function - call this regularly
    void loop()
    {
        if (!_enabled)
        {
            return;
        }

        if (!_mqttClient.connected())
        {
            _isConnected = false;
            connect(); // Try to reconnect
        }
        else
        {
            _isConnected = true;
            _mqttClient.loop(); // Process incoming messages
        }
    }

    // Check if MQTT is connected
    bool isConnected()
    {
        return _isConnected && _mqttClient.connected();
    }

    // Disconnect from MQTT broker
    void disconnect()
    {
        if (_mqttClient.connected())
        {
            publishAvailability(false);
            _mqttClient.disconnect();
        }
        _isConnected = false;
    }

    // Configuration methods
    void setBroker(const String &broker)
    {
        _broker = broker;
        saveToEEPROM();
    }

    void setPort(int port)
    {
        _port = port;
        saveToEEPROM();
    }

    void setCredentials(const String &username, const String &password)
    {
        _username = username;
        _password = password;
        saveToEEPROM();
    }

    void setEnabled(bool enabled)
    {
        _enabled = enabled;
        saveToEEPROM();

        if (!enabled && _mqttClient.connected())
        {
            disconnect();
        }
    }

    // Getters
    String getBroker() const { return _broker; }
    int getPort() const { return _port; }
    String getUsername() const { return _username; }
    bool isEnabled() const { return _enabled; }
};

#endif // MQTT_MANAGER_H
