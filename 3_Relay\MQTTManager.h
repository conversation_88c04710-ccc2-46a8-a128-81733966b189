#ifndef MQTT_MANAGER_H
#define MQTT_MANAGER_H

#include <Arduino.h>
#include <ESP8266WiFi.h>
#include <PubSubClient.h>
#include <EEPROM.h>
#include "DeviceManager.h"

// EEPROM layout for MQTT settings
#define MQTT_BROKER_ADDR 200
#define MQTT_BROKER_SIZE 64
#define MQTT_PORT_ADDR (MQTT_BROKER_ADDR + MQTT_BROKER_SIZE)
#define MQTT_PORT_SIZE 2
#define MQTT_USERNAME_ADDR (MQTT_PORT_ADDR + MQTT_PORT_SIZE)
#define MQTT_USERNAME_SIZE 32
#define MQTT_PASSWORD_ADDR (MQTT_USERNAME_ADDR + MQTT_USERNAME_SIZE)
#define MQTT_PASSWORD_SIZE 32
#define MQTT_ENABLED_ADDR (MQTT_PASSWORD_ADDR + MQTT_PASSWORD_SIZE)

// Default MQTT settings
#define DEFAULT_MQTT_BROKER "192.168.1.100"
#define DEFAULT_MQTT_PORT 1883
#define DEFAULT_MQTT_USERNAME ""
#define DEFAULT_MQTT_PASSWORD ""

// MQTT topics
#define TOPIC_PREFIX "home/switches/"
#define TOPIC_SUFFIX_SET "/set"
#define TOPIC_SUFFIX_STATE "/state"
#define TOPIC_SUFFIX_AVAILABLE "/available"
#define TOPIC_SUFFIX_NAME "/name"
#define TOPIC_SUFFIX_NAME_SET "/name/set"
#define TOPIC_SUFFIX_RGB_OFF_SET "/rgb/off/set"
#define TOPIC_SUFFIX_RGB_ON_SET "/rgb/on/set"
#define TOPIC_SUFFIX_RGB_OFF_STATE "/rgb/off/state"
#define TOPIC_SUFFIX_RGB_ON_STATE "/rgb/on/state"
#define TOPIC_ALL "home/switches/all"

class MQTTManager
{
private:
    WiFiClient _wifiClient;
    PubSubClient _mqttClient;
    DeviceManager *_deviceManager;
    String _broker;
    int _port;
    String _username;
    String _password;
    bool _enabled;
    unsigned long _lastConnectionAttempt;
    unsigned long _connectionRetryInterval;
    bool _isConnected;

    // Read string from EEPROM at specified address
    String readStringFromEEPROM(int startAddr, int maxLength)
    {
        char data[maxLength + 1];
        for (int i = 0; i < maxLength; i++)
        {
            data[i] = char(EEPROM.read(startAddr + i));
        }
        data[maxLength] = '\0';
        return String(data);
    }

    // Write string to EEPROM at specified address
    void writeStringToEEPROM(int startAddr, const String &strToWrite, int maxLength)
    {
        for (int i = 0; i < maxLength; i++)
        {
            if (i < strToWrite.length())
            {
                EEPROM.write(startAddr + i, strToWrite[i]);
            }
            else
            {
                EEPROM.write(startAddr + i, 0);
            }
        }
        EEPROM.commit();
    }

    // Load MQTT settings from EEPROM
    void loadFromEEPROM()
    {
        // Check if MQTT settings have been initialized
        if (EEPROM.read(MQTT_ENABLED_ADDR) == 0xFF)
        {
            // First time initialization - save defaults
            _broker = DEFAULT_MQTT_BROKER;
            _port = DEFAULT_MQTT_PORT;
            _username = DEFAULT_MQTT_USERNAME;
            _password = DEFAULT_MQTT_PASSWORD;
            _enabled = false; // Disabled by default

            saveToEEPROM();
            Serial.println("3-Relay MQTT settings initialized with defaults");
        }
        else
        {
            // Load existing settings
            _broker = readStringFromEEPROM(MQTT_BROKER_ADDR, MQTT_BROKER_SIZE);
            _username = readStringFromEEPROM(MQTT_USERNAME_ADDR, MQTT_USERNAME_SIZE);
            _password = readStringFromEEPROM(MQTT_PASSWORD_ADDR, MQTT_PASSWORD_SIZE);
            _enabled = EEPROM.read(MQTT_ENABLED_ADDR) == 1;

            // Load port (stored as 2 bytes)
            _port = EEPROM.read(MQTT_PORT_ADDR) | (EEPROM.read(MQTT_PORT_ADDR + 1) << 8);

            Serial.println("3-Relay MQTT settings loaded from EEPROM");
        }

        Serial.print("3-Relay MQTT Broker: ");
        Serial.println(_broker);
        Serial.print("3-Relay MQTT Port: ");
        Serial.println(_port);
        Serial.print("3-Relay MQTT Enabled: ");
        Serial.println(_enabled ? "Yes" : "No");
    }

    // Save MQTT settings to EEPROM
    void saveToEEPROM()
    {
        writeStringToEEPROM(MQTT_BROKER_ADDR, _broker, MQTT_BROKER_SIZE);
        writeStringToEEPROM(MQTT_USERNAME_ADDR, _username, MQTT_USERNAME_SIZE);
        writeStringToEEPROM(MQTT_PASSWORD_ADDR, _password, MQTT_PASSWORD_SIZE);
        EEPROM.write(MQTT_ENABLED_ADDR, _enabled ? 1 : 0);

        // Save port (as 2 bytes)
        EEPROM.write(MQTT_PORT_ADDR, _port & 0xFF);
        EEPROM.write(MQTT_PORT_ADDR + 1, (_port >> 8) & 0xFF);

        EEPROM.commit();
        Serial.println("3-Relay MQTT settings saved to EEPROM");
    }

public:
    MQTTManager(DeviceManager *deviceManager)
        : _mqttClient(_wifiClient), _deviceManager(deviceManager), _lastConnectionAttempt(0),
          _connectionRetryInterval(30000), _isConnected(false) // 30 seconds retry interval
    {
    }

    // Initialize MQTT manager
    void begin()
    {
        // Load settings from EEPROM
        loadFromEEPROM();

        if (_enabled)
        {
            // Set MQTT server
            _mqttClient.setServer(_broker.c_str(), _port);

            Serial.println("3-Relay MQTT Manager initialized");
        }
        else
        {
            Serial.println("3-Relay MQTT is disabled");
        }
    }

    // Set callback function for incoming messages
    void setCallback(void (*callback)(char *, byte *, unsigned int))
    {
        _mqttClient.setCallback(callback);
    }

    // Connect to MQTT broker
    bool connect()
    {
        if (!_enabled)
        {
            return false;
        }

        if (_mqttClient.connected())
        {
            return true;
        }

        // Don't try to reconnect too frequently
        unsigned long now = millis();
        if (now - _lastConnectionAttempt < _connectionRetryInterval)
        {
            return false;
        }
        _lastConnectionAttempt = now;

        Serial.println("3-Relay attempting MQTT connection...");

        // Create a client ID based on the device ID
        String clientId = "ESP8266_3Relay_" + _deviceManager->getDeviceID();

        bool connected = false;
        if (_username.length() > 0)
        {
            connected = _mqttClient.connect(clientId.c_str(), _username.c_str(), _password.c_str());
        }
        else
        {
            connected = _mqttClient.connect(clientId.c_str());
        }

        if (connected)
        {
            _isConnected = true;
            Serial.println("3-Relay MQTT connected");

            // Subscribe to control topics
            subscribeToTopics();

            // Publish device availability
            publishAvailability(true);

            // Publish all current states
            publishAllStates();

            return true;
        }
        else
        {
            _isConnected = false;
            Serial.print("3-Relay MQTT connection failed, rc=");
            Serial.println(_mqttClient.state());
            return false;
        }
    }

    // Main loop function - call this regularly
    void loop()
    {
        if (!_enabled)
        {
            return;
        }

        if (!_mqttClient.connected())
        {
            _isConnected = false;
            connect(); // Try to reconnect
        }
        else
        {
            _isConnected = true;
            _mqttClient.loop(); // Process incoming messages
        }
    }

    // Check if MQTT is connected
    bool isConnected()
    {
        return _isConnected && _mqttClient.connected();
    }

    // Simplified methods for 3-Relay variant
    void subscribeToTopics() { /* Simplified */ }
    void publishAvailability(bool available) { /* Simplified */ }
    void publishAllStates() { /* Simplified */ }
    void publishDeviceName() { /* Simplified */ }
    void publishSwitchState(uint8_t switchIndex) { /* Simplified */ }
    void publishRGBOffState(uint8_t switchIndex) { /* Simplified */ }
    void publishRGBOnState(uint8_t switchIndex) { /* Simplified */ }

    // Handle incoming MQTT messages (simplified)
    void handleMessage(char *topic, byte *payload, unsigned int length)
    {
        Serial.println("3-Relay MQTT message received (simplified handler)");
    }
};

#endif // MQTT_MANAGER_H
