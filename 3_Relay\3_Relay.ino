#include "WiFiManager.h"
#include "WebServerManager.h"
#include "DeviceManager.h"
#include "MQTTManager.h"
#include "ShiftRegisterManager.h"
#include "TouchSensorManager.h"

// Constants
const char *AP_SSID = "ESP8266_3Relay_Setup";
const char *AP_PASSWORD = "12345678";
const int CONNECTION_CHECK_INTERVAL = 5000; // 5 seconds
const int MQTT_CHECK_INTERVAL = 2000;       // 2 seconds

// Global variables
WiFiManager wifiManager;
DeviceManager deviceManager(3);     // Initialize with 3 switches
TouchSensorManager touchManager(3); // Initialize with 3 touch sensors
WebServerManager webServerManager(&wifiManager, &deviceManager, &touchManager);
MQTTManager mqttManager(&deviceManager);
unsigned long lastConnectionCheck = 0;
unsigned long lastMqttCheck = 0;
bool setupMode = false;

// MQTT callback function
void mqttCallback(char *topic, byte *payload, unsigned int length)
{
    mqttManager.handleMessage(topic, payload, length);
}

// Touch sensor callback function
void touchCallback(uint8_t switchIndex)
{
    deviceManager.toggleSwitch(switchIndex);
    Serial.print("Touch triggered switch toggle for switch ");
    Serial.println(switchIndex + 1);
}

void setup()
{
    // Initialize serial communication
    Serial.begin(115200);
    delay(1000);
    Serial.println("\n\nESP8266 3-Relay Switch Setup");

    // Initialize EEPROM for all managers
    EEPROM.begin(512);

    // Initialize device manager
    deviceManager.begin();

    // Initialize touch sensor manager
    touchManager.begin();
    touchManager.setTouchCallback(touchCallback);

    // Always start the access point and web server
    WiFi.mode(WIFI_AP_STA);
    WiFi.softAP(AP_SSID, AP_PASSWORD);

    Serial.print("Access Point started with SSID: ");
    Serial.println(AP_SSID);
    Serial.print("IP address: ");
    Serial.println(WiFi.softAPIP());

    // Start web server
    webServerManager.begin();

    // Try to connect with stored credentials
    if (wifiManager.hasStoredCredentials())
    {
        Serial.println("Found stored WiFi credentials, attempting to connect...");
        if (wifiManager.connectWithStoredCredentials())
        {
            Serial.println("Successfully connected to WiFi with stored credentials");
            setupMode = false;

            // Initialize MQTT manager
            mqttManager.begin();
            mqttManager.setCallback(mqttCallback);

            // Try to connect to MQTT server
            if (mqttManager.connect())
            {
                Serial.println("Connected to MQTT server");
            }
            else
            {
                Serial.println("Failed to connect to MQTT server");
            }
        }
        else
        {
            Serial.println("Failed to connect with stored credentials");
            setupMode = true;
        }
    }
    else
    {
        Serial.println("No stored WiFi credentials found");
        setupMode = true;
    }
}

void loop()
{
    // Always handle web server clients
    webServerManager.handleClient();

    unsigned long currentMillis = millis();

    // Periodically check WiFi connection status
    if (currentMillis - lastConnectionCheck >= CONNECTION_CHECK_INTERVAL)
    {
        lastConnectionCheck = currentMillis;

        // Check if connection status has changed
        if (setupMode && wifiManager.isConnected())
        {
            Serial.println("WiFi connection successful!");
            Serial.print("Connected to: ");
            Serial.println(wifiManager.getSSID());
            Serial.print("IP address: ");
            Serial.println(wifiManager.getIP());

            // Update setup mode status but keep webserver running
            setupMode = false;
            Serial.println("Exiting setup mode but keeping webserver active");

            // Initialize MQTT manager if not already done
            mqttManager.begin();
            mqttManager.setCallback(mqttCallback);

            // Try to connect to MQTT server
            if (mqttManager.connect())
            {
                Serial.println("Connected to MQTT server");
            }
            else
            {
                Serial.println("Failed to connect to MQTT server");
            }
        }
        else if (!setupMode && !wifiManager.isConnected())
        {
            Serial.println("WiFi connection lost, attempting to reconnect...");
            if (!wifiManager.connectWithStoredCredentials())
            {
                Serial.println("Failed to reconnect, entering setup mode");
                enterSetupMode();
            }
        }

        // Always ensure we're in AP+STA mode to keep the webserver accessible
        if (WiFi.getMode() != WIFI_AP_STA)
        {
            Serial.println("Restoring AP+STA mode to keep webserver accessible");
            WiFi.mode(WIFI_AP_STA);

            // Make sure AP is running
            if (WiFi.softAPgetStationNum() == 0)
            {
                WiFi.softAP(AP_SSID, AP_PASSWORD);
                Serial.println("Restarted access point");
            }
        }
    }

    // Periodically check MQTT connection status
    if (currentMillis - lastMqttCheck >= MQTT_CHECK_INTERVAL)
    {
        lastMqttCheck = currentMillis;

        // Only try to reconnect if WiFi is connected
        if (wifiManager.isConnected())
        {
            // Handle MQTT connection and message processing
            mqttManager.loop();
        }
    }

    // Handle touch sensors (check every loop for responsiveness)
    touchManager.handleTouchSensors();
}

// Enter WiFi setup mode
void enterSetupMode()
{
    Serial.println("Entering WiFi setup mode");
    setupMode = true;

    // Make sure access point is running
    if (WiFi.getMode() != WIFI_AP_STA)
    {
        WiFi.mode(WIFI_AP_STA);
        WiFi.softAP(AP_SSID, AP_PASSWORD);

        Serial.print("Access Point started with SSID: ");
        Serial.println(AP_SSID);
        Serial.print("IP address: ");
        Serial.println(WiFi.softAPIP());

        // Start web server if not already running
        if (!webServerManager.isRunning())
        {
            webServerManager.begin();
        }
    }
}
