#ifndef WIFI_MANAGER_H
#define WIFI_MANAGER_H

#include <Arduino.h>
#include <ESP8266WiFi.h>
#include <EEPROM.h>

// EEPROM layout
#define EEPROM_SIZE 512
#define SSID_ADDR 0
#define SSID_SIZE 32
#define PASS_ADDR (SSID_ADDR + SSID_SIZE)
#define PASS_SIZE 64
#define HAS_CREDENTIALS_ADDR (PASS_ADDR + PASS_SIZE)

class WiFiManager
{
private:
    String _ssid;
    String _password;
    bool _connected;
    int _connectionTimeout;
    int _maxConnectionAttempts;

    // Read string from EEPROM at specified address
    String readStringFromEEPROM(int startAddr, int maxLength)
    {
        char data[maxLength + 1];
        for (int i = 0; i < maxLength; i++)
        {
            data[i] = char(EEPROM.read(startAddr + i));
        }
        data[maxLength] = '\0';
        return String(data);
    }

    // Write string to EEPROM at specified address
    void writeStringToEEPROM(int startAddr, const String &strToWrite, int maxLength)
    {
        for (int i = 0; i < maxLength; i++)
        {
            if (i < strToWrite.length())
            {
                EEPROM.write(startAddr + i, strToWrite[i]);
            }
            else
            {
                EEPROM.write(startAddr + i, 0);
            }
        }
        EEPROM.commit();
    }

public:
    // Always use exactly 3 connection attempts
    WiFiManager(int connectionTimeout = 10000)
        : _connected(false), _connectionTimeout(connectionTimeout), _maxConnectionAttempts(3)
    {
        // Initialize EEPROM
        EEPROM.begin(EEPROM_SIZE);
        Serial.println("3-Relay WiFi Manager EEPROM initialized");
    }

    // Check if credentials are stored in EEPROM
    bool hasStoredCredentials()
    {
        return EEPROM.read(HAS_CREDENTIALS_ADDR) == 1;
    }

    // Load credentials from EEPROM
    bool loadCredentials()
    {
        if (!hasStoredCredentials())
        {
            return false;
        }

        _ssid = readStringFromEEPROM(SSID_ADDR, SSID_SIZE);
        _password = readStringFromEEPROM(PASS_ADDR, PASS_SIZE);

        Serial.println("3-Relay loaded credentials from EEPROM:");
        Serial.print("SSID: ");
        Serial.println(_ssid);

        return true;
    }

    // Save credentials to EEPROM
    void saveCredentials(const String &ssid, const String &password)
    {
        _ssid = ssid;
        _password = password;

        writeStringToEEPROM(SSID_ADDR, _ssid, SSID_SIZE);
        writeStringToEEPROM(PASS_ADDR, _password, PASS_SIZE);
        EEPROM.write(HAS_CREDENTIALS_ADDR, 1);
        EEPROM.commit();

        Serial.println("3-Relay saved credentials to EEPROM");
    }

    // Clear stored credentials
    void clearCredentials()
    {
        EEPROM.write(HAS_CREDENTIALS_ADDR, 0);
        EEPROM.commit();
        _ssid = "";
        _password = "";
        Serial.println("3-Relay cleared stored credentials");
    }

    // Connect to WiFi with stored credentials
    bool connectWithStoredCredentials()
    {
        if (!loadCredentials())
        {
            Serial.println("3-Relay: No stored credentials found");
            return false;
        }

        // Make sure _ssid is properly set before connecting
        String ssid = _ssid;
        String password = _password;
        return connect(ssid, password);
    }

    // Connect to WiFi with provided credentials
    bool connect(const String &ssid, const String &password)
    {
        // Use local variables instead of setting member variables until connection is successful
        String tempSsid = ssid;
        String tempPassword = password;

        Serial.print("3-Relay connecting to WiFi: ");
        Serial.println(tempSsid);

        // Always maintain AP+STA mode to keep the webserver accessible
        WiFi.mode(WIFI_AP_STA);

        // Disconnect first to ensure a clean connection attempt
        if (WiFi.status() == WL_CONNECTED)
        {
            Serial.println("3-Relay disconnecting from current network before connecting to new one");
            WiFi.disconnect(false); // false = don't disable the station
            delay(100);
        }

        WiFi.begin(tempSsid.c_str(), tempPassword.c_str());

        // Always try exactly 3 times before giving up
        for (int attempts = 1; attempts <= _maxConnectionAttempts; attempts++)
        {
            Serial.print("3-Relay attempt ");
            Serial.print(attempts);
            Serial.print(" of ");
            Serial.println(_maxConnectionAttempts);

            unsigned long startTime = millis();
            while (WiFi.status() != WL_CONNECTED && millis() - startTime < _connectionTimeout)
            {
                delay(500);
                Serial.print(".");
            }
            Serial.println();

            if (WiFi.status() == WL_CONNECTED)
            {
                // Only set member variables when connection is successful
                _ssid = tempSsid;
                _password = tempPassword;
                _connected = true;
                Serial.println("3-Relay connected to WiFi!");
                Serial.print("IP address: ");
                Serial.println(WiFi.localIP());
                return true;
            }

            // If this is not the last attempt, try again
            if (attempts < _maxConnectionAttempts)
            {
                Serial.println("3-Relay failed to connect, retrying...");
                WiFi.disconnect(false); // false = don't disable the station
                delay(1000);
                // Make sure we're still in AP+STA mode
                WiFi.mode(WIFI_AP_STA);
                WiFi.begin(tempSsid.c_str(), tempPassword.c_str());
            }
        }

        _connected = false;
        Serial.println("3-Relay failed to connect to WiFi after 3 attempts");
        Serial.println("3-Relay connection failed - NOT saving credentials");
        // Make sure we don't have any credentials stored for this failed connection
        // This is a safeguard in case something else is saving credentials
        if (_ssid == tempSsid)
        {
            Serial.println("3-Relay clearing any stored credentials for this network");
            clearCredentials();
        }
        return false;
    }

    // Check if connected to WiFi
    bool isConnected()
    {
        return WiFi.status() == WL_CONNECTED;
    }

    // Disconnect from WiFi
    void disconnect()
    {
        WiFi.disconnect();
        _connected = false;
    }

    // Get current SSID
    String getSSID()
    {
        // First try to get the SSID directly from the WiFi library if connected
        if (isConnected())
        {
            String currentSSID = WiFi.SSID();
            if (currentSSID.length() > 0)
            {
                return currentSSID;
            }
        }
        // Fall back to stored SSID
        return _ssid;
    }

    // Get current IP address
    IPAddress getIP()
    {
        return WiFi.localIP();
    }
};

#endif // WIFI_MANAGER_H
