#ifndef MQTT_MANAGER_H
#define MQTT_MANAGER_H

#include <Arduino.h>
#include <PubSubClient.h>
#include <WiFiClient.h>
#include <EEPROM.h>
#include "DeviceManager.h"

// EEPROM layout for MQTT settings
#define MQTT_SERVER_ADDR 200
#define MQTT_SERVER_SIZE 64
#define MQTT_PORT_ADDR (MQTT_SERVER_ADDR + MQTT_SERVER_SIZE)
#define MQTT_USER_ADDR (MQTT_PORT_ADDR + 2)
#define MQTT_USER_SIZE 32
#define MQTT_PASS_ADDR (MQTT_USER_ADDR + MQTT_USER_SIZE)
#define MQTT_PASS_SIZE 32

// Default MQTT settings
#define DEFAULT_MQTT_SERVER "mqtt.example.com"
#define DEFAULT_MQTT_PORT 1883
#define DEFAULT_MQTT_USER ""
#define DEFAULT_MQTT_PASS ""

// MQTT topics
#define TOPIC_PREFIX "home/switches/"
#define TOPIC_SUFFIX_SET "/set"
#define TOPIC_SUFFIX_STATE "/state"
#define TOPIC_SUFFIX_AVAILABLE "/available"
#define TOPIC_SUFFIX_NAME "/name"
#define TOPIC_SUFFIX_NAME_SET "/name/set"
#define TOPIC_SUFFIX_RGB_SET "/rgb/set"
#define TOPIC_SUFFIX_RGB_STATE "/rgb/state"
#define TOPIC_ALL "home/switches/all"

class MQTTManager
{
private:
    WiFiClient _wifiClient;
    PubSubClient _mqttClient;
    DeviceManager *_deviceManager;

    String _mqttServer;
    uint16_t _mqttPort;
    String _mqttUser;
    String _mqttPassword;

    unsigned long _lastReconnectAttempt = 0;
    const unsigned long _reconnectInterval = 5000; // 5 seconds
    bool _connected = false;

    // Callback function for incoming messages
    static void callback(char *topic, byte *payload, unsigned int length)
    {
        // This is a static method, so we can't access instance variables directly
        // The actual implementation will be in the main file
    }

    // Read string from EEPROM at specified address
    String readStringFromEEPROM(int startAddr, int maxLength)
    {
        char data[maxLength + 1];
        for (int i = 0; i < maxLength; i++)
        {
            data[i] = char(EEPROM.read(startAddr + i));
        }
        data[maxLength] = '\0';
        return String(data);
    }

    // Write string to EEPROM at specified address
    void writeStringToEEPROM(int startAddr, const String &strToWrite, int maxLength)
    {
        for (int i = 0; i < maxLength; i++)
        {
            if (i < strToWrite.length())
            {
                EEPROM.write(startAddr + i, strToWrite[i]);
            }
            else
            {
                EEPROM.write(startAddr + i, 0);
            }
        }
        EEPROM.commit();
    }

    // Read uint16_t from EEPROM
    uint16_t readUint16FromEEPROM(int addr)
    {
        uint16_t value = EEPROM.read(addr);
        value |= (EEPROM.read(addr + 1) << 8);
        return value;
    }

    // Write uint16_t to EEPROM
    void writeUint16ToEEPROM(int addr, uint16_t value)
    {
        EEPROM.write(addr, value & 0xFF);
        EEPROM.write(addr + 1, (value >> 8) & 0xFF);
        EEPROM.commit();
    }

public:
    MQTTManager(DeviceManager *deviceManager)
        : _mqttClient(_wifiClient), _deviceManager(deviceManager)
    {
    }

    void begin()
    {
        // Load MQTT settings from EEPROM or use defaults
        if (EEPROM.read(MQTT_SERVER_ADDR) == 0xFF)
        {
            // First time initialization
            _mqttServer = DEFAULT_MQTT_SERVER;
            _mqttPort = DEFAULT_MQTT_PORT;
            _mqttUser = DEFAULT_MQTT_USER;
            _mqttPassword = DEFAULT_MQTT_PASS;

            // Save to EEPROM
            writeStringToEEPROM(MQTT_SERVER_ADDR, _mqttServer, MQTT_SERVER_SIZE);
            writeUint16ToEEPROM(MQTT_PORT_ADDR, _mqttPort);
            writeStringToEEPROM(MQTT_USER_ADDR, _mqttUser, MQTT_USER_SIZE);
            writeStringToEEPROM(MQTT_PASS_ADDR, _mqttPassword, MQTT_PASS_SIZE);

            Serial.println("MQTT settings initialized with defaults");
        }
        else
        {
            // Load existing configuration
            loadFromEEPROM();
        }

        // Set up MQTT client
        _mqttClient.setServer(_mqttServer.c_str(), _mqttPort);

        // The callback will be set in the main file

        Serial.println("MQTT Manager initialized");
        Serial.print("MQTT Server: ");
        Serial.println(_mqttServer);
        Serial.print("MQTT Port: ");
        Serial.println(_mqttPort);
    }

    // Load MQTT settings from EEPROM
    void loadFromEEPROM()
    {
        _mqttServer = readStringFromEEPROM(MQTT_SERVER_ADDR, MQTT_SERVER_SIZE);
        _mqttPort = readUint16FromEEPROM(MQTT_PORT_ADDR);
        _mqttUser = readStringFromEEPROM(MQTT_USER_ADDR, MQTT_USER_SIZE);
        _mqttPassword = readStringFromEEPROM(MQTT_PASS_ADDR, MQTT_PASS_SIZE);

        // Validate port
        if (_mqttPort == 0 || _mqttPort > 65535)
        {
            _mqttPort = DEFAULT_MQTT_PORT;
            writeUint16ToEEPROM(MQTT_PORT_ADDR, _mqttPort);
        }

        Serial.println("MQTT settings loaded from EEPROM");
    }

    // Save MQTT settings to EEPROM
    void saveToEEPROM()
    {
        writeStringToEEPROM(MQTT_SERVER_ADDR, _mqttServer, MQTT_SERVER_SIZE);
        writeUint16ToEEPROM(MQTT_PORT_ADDR, _mqttPort);
        writeStringToEEPROM(MQTT_USER_ADDR, _mqttUser, MQTT_USER_SIZE);
        writeStringToEEPROM(MQTT_PASS_ADDR, _mqttPassword, MQTT_PASS_SIZE);

        Serial.println("MQTT settings saved to EEPROM");
    }

    // Set MQTT server settings
    void setServer(const String &server, uint16_t port)
    {
        _mqttServer = server;
        _mqttPort = port;
        _mqttClient.setServer(_mqttServer.c_str(), _mqttPort);
        saveToEEPROM();
    }

    // Set MQTT credentials
    void setCredentials(const String &user, const String &password)
    {
        _mqttUser = user;
        _mqttPassword = password;
        saveToEEPROM();
    }

    // Set callback function
    void setCallback(MQTT_CALLBACK_SIGNATURE)
    {
        _mqttClient.setCallback(callback);
    }

    // Connect to MQTT server
    bool connect()
    {
        if (_mqttClient.connected())
        {
            return true;
        }

        Serial.print("Connecting to MQTT server: ");
        Serial.println(_mqttServer);

        // Create a client ID based on the device ID
        String clientId = "ESP8266_" + _deviceManager->getDeviceID();

        // Last Will and Testament message
        String willTopic = String(TOPIC_PREFIX) + _deviceManager->getDeviceID() + TOPIC_SUFFIX_AVAILABLE;
        const char *willMessage = "offline";

        bool success;
        if (_mqttUser.length() > 0)
        {
            success = _mqttClient.connect(clientId.c_str(), _mqttUser.c_str(), _mqttPassword.c_str(),
                                          willTopic.c_str(), 0, true, willMessage);
        }
        else
        {
            success = _mqttClient.connect(clientId.c_str(), willTopic.c_str(), 0, true, willMessage);
        }

        if (success)
        {
            Serial.println("Connected to MQTT server");
            _connected = true;

            // Publish online status
            publish(willTopic.c_str(), "online", true);

            // Subscribe to control topics
            String controlTopic = String(TOPIC_PREFIX) + _deviceManager->getDeviceID() + "/#";
            _mqttClient.subscribe(controlTopic.c_str());

            // Subscribe to all devices topic
            _mqttClient.subscribe(TOPIC_ALL);

            // Subscribe to device name set topic
            String nameSetTopic = String(TOPIC_PREFIX) + _deviceManager->getDeviceID() + TOPIC_SUFFIX_NAME_SET;
            _mqttClient.subscribe(nameSetTopic.c_str());

            // Publish initial states
            publishAllStates();

            return true;
        }
        else
        {
            Serial.print("Failed to connect to MQTT server, rc=");
            Serial.println(_mqttClient.state());
            _connected = false;
            return false;
        }
    }

    // Publish a message to a topic
    void publish(const char *topic, const char *payload, bool retained = false)
    {
        if (_mqttClient.connected())
        {
            _mqttClient.publish(topic, payload, retained);
        }
    }

    // Publish all switch states
    void publishAllStates()
    {
        for (uint8_t i = 0; i < _deviceManager->getSwitchCount(); i++)
        {
            publishSwitchState(i);
            publishRGBState(i);
        }

        // Also publish device name
        publishDeviceName();
    }

    // Publish device name
    void publishDeviceName()
    {
        String topic = String(TOPIC_PREFIX) + _deviceManager->getDeviceID() + TOPIC_SUFFIX_NAME;
        String payload = _deviceManager->getDeviceName();

        publish(topic.c_str(), payload.c_str(), true);
    }

    // Publish a specific switch state
    void publishSwitchState(uint8_t switchIndex)
    {
        if (switchIndex >= _deviceManager->getSwitchCount())
        {
            return;
        }

        String topic = String(TOPIC_PREFIX) + _deviceManager->getDeviceID() + "/switch/" + switchIndex + TOPIC_SUFFIX_STATE;
        String payload = _deviceManager->getSwitchState(switchIndex) ? "ON" : "OFF";

        publish(topic.c_str(), payload.c_str(), true);
    }

    // Publish a specific RGB state
    void publishRGBState(uint8_t switchIndex)
    {
        if (switchIndex >= _deviceManager->getSwitchCount())
        {
            return;
        }

        uint8_t r, g, b;
        _deviceManager->getRGB(switchIndex, r, g, b);

        String topic = String(TOPIC_PREFIX) + _deviceManager->getDeviceID() + "/switch/" + switchIndex + TOPIC_SUFFIX_RGB_STATE;
        String payload = "{\"r\":" + String(r) + ",\"g\":" + String(g) + ",\"b\":" + String(b) + "}";

        publish(topic.c_str(), payload.c_str(), true);
    }

    // Handle incoming messages
    void handleMessage(char *topic, byte *payload, unsigned int length)
    {
        // Convert payload to string
        char message[length + 1];
        memcpy(message, payload, length);
        message[length] = '\0';

        String topicStr = String(topic);
        String messageStr = String(message);

        Serial.print("Message arrived [");
        Serial.print(topicStr);
        Serial.print("] ");
        Serial.println(messageStr);

        // Check if it's a control message for a specific switch
        String deviceIdStr = _deviceManager->getDeviceID();
        String prefix = String(TOPIC_PREFIX) + deviceIdStr + "/switch/";

        if (topicStr.startsWith(prefix) && topicStr.endsWith(TOPIC_SUFFIX_SET))
        {
            // Extract switch index
            int prefixLength = prefix.length();
            int suffixLength = String(TOPIC_SUFFIX_SET).length();
            String switchIndexStr = topicStr.substring(prefixLength, topicStr.length() - suffixLength);

            uint8_t switchIndex = switchIndexStr.toInt();

            // Set switch state
            if (messageStr == "ON")
            {
                _deviceManager->setSwitchState(switchIndex, true);
                publishSwitchState(switchIndex);
            }
            else if (messageStr == "OFF")
            {
                _deviceManager->setSwitchState(switchIndex, false);
                publishSwitchState(switchIndex);
            }
            else if (messageStr == "TOGGLE")
            {
                _deviceManager->toggleSwitch(switchIndex);
                publishSwitchState(switchIndex);
            }
        }

        // Check if it's an RGB control message for a specific switch
        if (topicStr.startsWith(prefix) && topicStr.endsWith(TOPIC_SUFFIX_RGB_SET))
        {
            // Extract switch index
            int prefixLength = prefix.length();
            int suffixLength = String(TOPIC_SUFFIX_RGB_SET).length();
            String switchIndexStr = topicStr.substring(prefixLength, topicStr.length() - suffixLength);

            uint8_t switchIndex = switchIndexStr.toInt();

            // Parse JSON RGB message: {"r":255,"g":128,"b":0}
            if (messageStr.startsWith("{") && messageStr.endsWith("}"))
            {
                // Simple JSON parsing for RGB values
                int rIndex = messageStr.indexOf("\"r\":");
                int gIndex = messageStr.indexOf("\"g\":");
                int bIndex = messageStr.indexOf("\"b\":");

                if (rIndex != -1 && gIndex != -1 && bIndex != -1)
                {
                    uint8_t r = messageStr.substring(rIndex + 4, messageStr.indexOf(",", rIndex)).toInt();
                    uint8_t g = messageStr.substring(gIndex + 4, messageStr.indexOf(",", gIndex)).toInt();
                    uint8_t b = messageStr.substring(bIndex + 4, messageStr.indexOf("}", bIndex)).toInt();

                    _deviceManager->setRGB(switchIndex, r, g, b);
                    publishRGBState(switchIndex);
                }
            }
        }

        // Check if it's a device name change message
        String nameSetTopic = String(TOPIC_PREFIX) + deviceIdStr + TOPIC_SUFFIX_NAME_SET;
        if (topicStr == nameSetTopic)
        {
            // Update device name
            if (messageStr.length() > 0)
            {
                Serial.print("Changing device name to: ");
                Serial.println(messageStr);
                _deviceManager->setDeviceName(messageStr);

                // Publish the new name to confirm the change
                publishDeviceName();
            }
        }

        // Check if it's a message for all switches
        if (topicStr == TOPIC_ALL)
        {
            if (messageStr == "ON" || messageStr == "OFF")
            {
                bool state = (messageStr == "ON");
                for (uint8_t i = 0; i < _deviceManager->getSwitchCount(); i++)
                {
                    _deviceManager->setSwitchState(i, state);
                }
                publishAllStates();
            }
        }
    }

    // Check connection and reconnect if necessary
    void loop()
    {
        if (!_mqttClient.connected())
        {
            _connected = false;
            unsigned long now = millis();
            if (now - _lastReconnectAttempt > _reconnectInterval)
            {
                _lastReconnectAttempt = now;
                if (connect())
                {
                    _lastReconnectAttempt = 0;
                }
            }
        }
        else
        {
            _mqttClient.loop();
        }
    }

    // Check if connected to MQTT server
    bool isConnected()
    {
        return _connected;
    }
};

#endif // MQTT_MANAGER_H
