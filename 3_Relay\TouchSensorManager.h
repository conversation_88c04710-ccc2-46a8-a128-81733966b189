#ifndef TOUCH_SENSOR_MANAGER_H
#define TOUCH_SENSOR_MANAGER_H

#include <Arduino.h>
#include <EEPROM.h>

// EEPROM layout for touch sensor settings (3-Relay variant)
#define TOUCH_PINS_EEPROM_ADDR 300
#define TOUCH_DEBOUNCE_EEPROM_ADDR (TOUCH_PINS_EEPROM_ADDR + 3)

class TouchSensorManager
{
private:
    uint8_t _touchPins[3];              // Touch sensor pins for each switch
    bool _lastTouchState[3];            // Last touch state for debouncing
    unsigned long _lastTouchTime[3];    // Last touch time for debouncing
    unsigned long _debounceDelay;       // Debounce delay in milliseconds
    uint8_t _switchCount;               // Number of active switches
    
    // Callback function pointer for touch events
    void (*_touchCallback)(uint8_t switchIndex);

public:
    TouchSensorManager(uint8_t switchCount = 3)
        : _switchCount(switchCount), _debounceDelay(50), _touchCallback(nullptr)
    {
        // Touch sensor pins for ESP8266 3-Relay variant
        const uint8_t DEFAULT_PINS[3] = {4, 5, 12}; // t1, t2, t3
        
        // Initialize arrays
        for (int i = 0; i < 3; i++)
        {
            _touchPins[i] = DEFAULT_PINS[i];
            _lastTouchState[i] = false;
            _lastTouchTime[i] = 0;
        }
    }

    // Initialize touch sensors
    void begin()
    {
        // Load settings from EEPROM or use defaults
        loadFromEEPROM();
        
        // Initialize GPIO pins
        for (int i = 0; i < _switchCount; i++)
        {
            pinMode(_touchPins[i], INPUT_PULLUP);
            Serial.print("3-Relay Touch sensor ");
            Serial.print(i + 1);
            Serial.print(" initialized on pin ");
            Serial.println(_touchPins[i]);
        }
        
        Serial.println("3-Relay Touch Sensor Manager initialized");
    }

    // Set callback function for touch events
    void setTouchCallback(void (*callback)(uint8_t switchIndex))
    {
        _touchCallback = callback;
    }

    // Set touch pin for a specific switch
    void setTouchPin(uint8_t switchIndex, uint8_t pin)
    {
        if (switchIndex < _switchCount && pin < 20)
        {
            _touchPins[switchIndex] = pin;
            pinMode(pin, INPUT_PULLUP);
            saveToEEPROM();
            
            Serial.print("Touch pin for switch ");
            Serial.print(switchIndex + 1);
            Serial.print(" set to ");
            Serial.println(pin);
        }
    }

    // Get touch pin for a specific switch
    uint8_t getTouchPin(uint8_t switchIndex)
    {
        if (switchIndex < _switchCount)
        {
            return _touchPins[switchIndex];
        }
        return 0;
    }

    // Set debounce delay
    void setDebounceDelay(unsigned long delay)
    {
        _debounceDelay = delay;
        saveToEEPROM();
    }

    // Get debounce delay
    unsigned long getDebounceDelay()
    {
        return _debounceDelay;
    }

    // Read touch sensor state (active LOW)
    bool readTouchSensor(uint8_t switchIndex)
    {
        if (switchIndex < _switchCount)
        {
            return digitalRead(_touchPins[switchIndex]) == LOW;
        }
        return false;
    }

    // Handle touch sensors - call this in main loop
    void handleTouchSensors()
    {
        unsigned long currentTime = millis();
        
        for (uint8_t i = 0; i < _switchCount; i++)
        {
            bool currentTouch = readTouchSensor(i);
            
            // Check for touch press (transition from not touched to touched)
            if (currentTouch && !_lastTouchState[i] && 
                (currentTime - _lastTouchTime[i] > _debounceDelay))
            {
                _lastTouchTime[i] = currentTime;
                
                Serial.print("3-Relay Touch sensor ");
                Serial.print(i + 1);
                Serial.print(" (pin ");
                Serial.print(_touchPins[i]);
                Serial.println(") pressed");
                
                // Call callback function if set
                if (_touchCallback)
                {
                    _touchCallback(i);
                }
            }
            
            _lastTouchState[i] = currentTouch;
        }
    }

    // Load settings from EEPROM
    void loadFromEEPROM()
    {
        // Check if EEPROM has been initialized
        if (EEPROM.read(TOUCH_PINS_EEPROM_ADDR) == 0xFF)
        {
            // First time initialization - save defaults
            saveToEEPROM();
            Serial.println("3-Relay touch sensor settings initialized with defaults");
        }
        else
        {
            // Load existing settings
            for (int i = 0; i < 3; i++)
            {
                uint8_t pin = EEPROM.read(TOUCH_PINS_EEPROM_ADDR + i);
                if (pin < 20) // Valid pin range
                {
                    _touchPins[i] = pin;
                }
            }
            
            // Load debounce delay
            _debounceDelay = EEPROM.read(TOUCH_DEBOUNCE_EEPROM_ADDR);
            if (_debounceDelay == 0xFF || _debounceDelay == 0)
            {
                _debounceDelay = 50; // Default 50ms
            }
            
            Serial.println("3-Relay touch sensor settings loaded from EEPROM");
        }
    }

    // Save settings to EEPROM
    void saveToEEPROM()
    {
        for (int i = 0; i < 3; i++)
        {
            EEPROM.write(TOUCH_PINS_EEPROM_ADDR + i, _touchPins[i]);
        }
        EEPROM.write(TOUCH_DEBOUNCE_EEPROM_ADDR, _debounceDelay);
        EEPROM.commit();
        
        Serial.println("3-Relay touch sensor settings saved to EEPROM");
    }

    // Get switch count
    uint8_t getSwitchCount()
    {
        return _switchCount;
    }

    // Set switch count
    void setSwitchCount(uint8_t count)
    {
        if (count <= 3)
        {
            _switchCount = count;
        }
    }

    // Test all touch sensors (for debugging)
    void testTouchSensors()
    {
        Serial.println("Testing 3-Relay touch sensors...");
        for (int i = 0; i < _switchCount; i++)
        {
            bool state = readTouchSensor(i);
            Serial.print("Touch sensor ");
            Serial.print(i + 1);
            Serial.print(" (pin ");
            Serial.print(_touchPins[i]);
            Serial.print("): ");
            Serial.println(state ? "PRESSED" : "NOT PRESSED");
        }
    }
};

#endif // TOUCH_SENSOR_MANAGER_H
