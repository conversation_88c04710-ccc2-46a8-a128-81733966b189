#ifndef SHIFT_REGISTER_MANAGER_H
#define SHIFT_REGISTER_MANAGER_H

#include <Arduino.h>

class ShiftRegisterManager
{
private:
    uint8_t _dataPin;
    uint8_t _clockPin;
    uint8_t _latchPin;
    uint16_t _outputState;

    // Hardware-specific bit mapping for ESP8266 dual shift register setup
    // Data structure: x1x2x3x4x5x6x7x8 x9x10x11x12x13x14x15x16
    // First 74HC595 (x1-x8):       Second 74HC595 (x9-x16):
    // x1 -> r4                     x9  -> g3
    // x2 -> g4                     x10 -> b3
    // x3 -> b4                     x11 -> r3
    // x4 -> R1 (Relay 1)           x12 -> b2
    // x5 -> R4 (Relay 4)           x13 -> g2
    // x6 -> R3 (Relay 3)           x14 -> b1
    // x7 -> R2 (Relay 2)           x15 -> g1
    // x8 -> r2                     x16 -> r1

    // Bit position arrays for hardware mapping
    const uint8_t RELAY_BITS[4] = {3, 6, 5, 4};   // R1, R2, R3, R4 bit positions (0-indexed)
    const uint8_t RED_BITS[4] = {15, 7, 10, 0};   // r1, r2, r3, r4 bit positions
    const uint8_t GREEN_BITS[4] = {14, 12, 8, 1}; // g1, g2, g3, g4 bit positions
    const uint8_t BLUE_BITS[4] = {13, 11, 9, 2};  // b1, b2, b3, b4 bit positions

    // Update the physical shift registers with current state
    void updateShiftRegisters()
    {
        digitalWrite(_latchPin, LOW);

        // Send bits 9-16 first (goes to second 74HC595)
        shiftOut(_dataPin, _clockPin, MSBFIRST, (_outputState >> 8) & 0xFF);

        // Send bits 1-8 second (stays in first 74HC595)
        shiftOut(_dataPin, _clockPin, MSBFIRST, _outputState & 0xFF);

        digitalWrite(_latchPin, HIGH);
    }

public:
    ShiftRegisterManager(uint8_t dataPin, uint8_t clockPin, uint8_t latchPin)
        : _dataPin(dataPin), _clockPin(clockPin), _latchPin(latchPin), _outputState(0)
    {
    }

    // Initialize the shift register pins
    void begin()
    {
        pinMode(_dataPin, OUTPUT);
        pinMode(_clockPin, OUTPUT);
        pinMode(_latchPin, OUTPUT);

        // Initialize all outputs to LOW
        _outputState = 0;
        updateShiftRegisters();

        Serial.println("Shift Register Manager initialized");
        Serial.print("Data Pin: ");
        Serial.println(_dataPin);
        Serial.print("Clock Pin: ");
        Serial.println(_clockPin);
        Serial.print("Latch Pin: ");
        Serial.println(_latchPin);
    }

    // Set relay state (relayIndex: 0-3 for relays 1-4)
    void setRelay(uint8_t relayIndex, bool state)
    {
        if (relayIndex >= 4)
            return;

        uint8_t bitPos = RELAY_BITS[relayIndex];

        if (state)
        {
            _outputState |= (1 << bitPos);
        }
        else
        {
            _outputState &= ~(1 << bitPos);
        }

        updateShiftRegisters();

        Serial.print("Relay ");
        Serial.print(relayIndex + 1);
        Serial.print(" set to ");
        Serial.println(state ? "ON" : "OFF");
    }

    // Get relay state
    bool getRelay(uint8_t relayIndex)
    {
        if (relayIndex >= 4)
            return false;

        uint8_t bitPos = RELAY_BITS[relayIndex];
        return (_outputState & (1 << bitPos)) != 0;
    }

    // Set RGB color for a specific relay button (relayIndex: 0-3 for relays 1-4)
    // Note: RGB values are digital (0=OFF, >0=ON) since 74HC595 provides digital outputs only
    void setRGB(uint8_t relayIndex, uint8_t r, uint8_t g, uint8_t b)
    {
        if (relayIndex >= 4)
            return;

        // Set Red (digital: 0=OFF, >0=ON)
        if (r > 0)
        {
            _outputState |= (1 << RED_BITS[relayIndex]);
        }
        else
        {
            _outputState &= ~(1 << RED_BITS[relayIndex]);
        }

        // Set Green (digital: 0=OFF, >0=ON)
        if (g > 0)
        {
            _outputState |= (1 << GREEN_BITS[relayIndex]);
        }
        else
        {
            _outputState &= ~(1 << GREEN_BITS[relayIndex]);
        }

        // Set Blue (digital: 0=OFF, >0=ON)
        if (b > 0)
        {
            _outputState |= (1 << BLUE_BITS[relayIndex]);
        }
        else
        {
            _outputState &= ~(1 << BLUE_BITS[relayIndex]);
        }

        updateShiftRegisters();

        Serial.print("RGB for Relay ");
        Serial.print(relayIndex + 1);
        Serial.print(" set to R:");
        Serial.print(r > 0 ? "ON" : "OFF");
        Serial.print(" G:");
        Serial.print(g > 0 ? "ON" : "OFF");
        Serial.print(" B:");
        Serial.println(b > 0 ? "ON" : "OFF");
    }

    // Get RGB color for a specific relay button
    void getRGB(uint8_t relayIndex, uint8_t &r, uint8_t &g, uint8_t &b)
    {
        if (relayIndex >= 4)
        {
            r = g = b = 0;
            return;
        }

        r = (_outputState & (1 << RED_BITS[relayIndex])) ? 255 : 0;
        g = (_outputState & (1 << GREEN_BITS[relayIndex])) ? 255 : 0;
        b = (_outputState & (1 << BLUE_BITS[relayIndex])) ? 255 : 0;
    }

    // Set individual output bit (for advanced control)
    void setOutput(uint8_t bitIndex, bool state)
    {
        if (bitIndex >= 16)
            return;

        if (state)
        {
            _outputState |= (1 << bitIndex);
        }
        else
        {
            _outputState &= ~(1 << bitIndex);
        }

        updateShiftRegisters();
    }

    // Get individual output bit state
    bool getOutput(uint8_t bitIndex)
    {
        if (bitIndex >= 16)
            return false;
        return (_outputState & (1 << bitIndex)) != 0;
    }

    // Set all outputs at once
    void setAllOutputs(uint16_t state)
    {
        _outputState = state;
        updateShiftRegisters();
    }

    // Get current output state
    uint16_t getAllOutputs()
    {
        return _outputState;
    }

    // Clear all outputs
    void clearAll()
    {
        _outputState = 0;
        updateShiftRegisters();
        Serial.println("All shift register outputs cleared");
    }
};

#endif // SHIFT_REGISTER_MANAGER_H
