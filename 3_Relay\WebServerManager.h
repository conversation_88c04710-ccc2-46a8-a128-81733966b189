#ifndef WEB_SERVER_MANAGER_H
#define WEB_SERVER_MANAGER_H

#include <Arduino.h>
#include <ESP8266WiFi.h>
#include <ESP8266WebServer.h>
#include "WiFiManager.h"
#include "DeviceManager.h"
#include "TouchSensorManager.h"

class WebServerManager
{
private:
    ESP8266WebServer _server;
    WiFiManager *_wifiManager;
    DeviceManager *_deviceManager;
    TouchSensorManager *_touchManager;
    bool _serverRunning;
    int _port;

    // HTML templates
    const char *_htmlHeader = R"(
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3-Relay Switch Control</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #1a1a1a;
            color: #e0e0e0;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: #2d2d2d;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
        }
        h1 {
            color: #4CAF50;
            text-align: center;
            margin-bottom: 30px;
        }
        h2 {
            color: #81C784;
            border-bottom: 2px solid #4CAF50;
            padding-bottom: 10px;
        }
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }
        .info-item {
            background-color: #3d3d3d;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #4CAF50;
        }
        .info-label {
            font-weight: bold;
            color: #81C784;
            margin-bottom: 5px;
        }
        .info-value {
            color: #e0e0e0;
            word-break: break-all;
        }
        .switch-container {
            background-color: #3d3d3d;
            margin-bottom: 20px;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #4CAF50;
        }
        .switch-state {
            font-weight: bold;
            padding: 5px 10px;
            border-radius: 4px;
            display: inline-block;
            margin-left: 10px;
        }
        .switch-state.on {
            background-color: #4CAF50;
            color: white;
        }
        .switch-state.off {
            background-color: #f44336;
            color: white;
        }
        .button-container {
            margin-top: 15px;
        }
        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
            margin-bottom: 10px;
            transition: background-color 0.3s;
        }
        button:hover {
            background-color: #45a049;
        }
        button:active {
            background-color: #3d8b40;
        }
        .nav-button {
            background-color: #2196F3;
        }
        .nav-button:hover {
            background-color: #1976D2;
        }
    </style>
</head>
<body>
    <div class="container">
)";

    const char *_htmlFooter = R"(
    </div>
    <script>
        function setRGBOff(switchIndex) {
            var r = document.getElementById('rOff' + switchIndex).checked ? 1 : 0;
            var g = document.getElementById('gOff' + switchIndex).checked ? 1 : 0;
            var b = document.getElementById('bOff' + switchIndex).checked ? 1 : 0;
            
            var xhr = new XMLHttpRequest();
            xhr.open('POST', '/rgb/off', true);
            xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
            xhr.send('index=' + switchIndex + '&r=' + r + '&g=' + g + '&b=' + b);
        }
        
        function setRGBOn(switchIndex) {
            var r = document.getElementById('rOn' + switchIndex).checked ? 1 : 0;
            var g = document.getElementById('gOn' + switchIndex).checked ? 1 : 0;
            var b = document.getElementById('bOn' + switchIndex).checked ? 1 : 0;
            
            var xhr = new XMLHttpRequest();
            xhr.open('POST', '/rgb/on', true);
            xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
            xhr.send('index=' + switchIndex + '&r=' + r + '&g=' + g + '&b=' + b);
        }
    </script>
</body>
</html>
)";

public:
    WebServerManager(WiFiManager *wifiManager, DeviceManager *deviceManager = nullptr, TouchSensorManager *touchManager = nullptr, int port = 80)
        : _server(port), _wifiManager(wifiManager), _deviceManager(deviceManager), _touchManager(touchManager), _serverRunning(false), _port(port)
    {
    }

    // Initialize and start the web server
    void begin()
    {
        // Main page
        _server.on("/", [this]()
                   { handleRoot(); });

        // Device control routes
        _server.on("/device", [this]()
                   { handleDevice(); });
        _server.on("/switch", [this]()
                   { handleSwitch(); });
        _server.on("/rgb/off", [this]()
                   { handleRGBOff(); });
        _server.on("/rgb/on", [this]()
                   { handleRGBOn(); });

        // 404 handler
        _server.onNotFound([this]()
                           { handle404(); });

        _server.begin();
        _serverRunning = true;

        Serial.println("3-Relay Web server started");
        Serial.print("3-Relay Web server running on port ");
        Serial.println(_port);
    }

    // Handle client requests
    void handleClient()
    {
        if (_serverRunning)
        {
            _server.handleClient();
        }
    }

    // Check if server is running
    bool isRunning()
    {
        return _serverRunning;
    }

    // Stop the web server
    void stop()
    {
        _server.stop();
        _serverRunning = false;
        Serial.println("3-Relay Web server stopped");
    }

private:
    // Handle root page
    void handleRoot()
    {
        String html = _htmlHeader;
        html += "<h1>3-Relay ESP8266 Switch Control</h1>";

        // WiFi Status
        html += "<h2>WiFi Status</h2>";
        html += "<div class='info-grid'>";
        html += "<div class='info-item'>";
        html += "<div class='info-label'>Status:</div>";
        html += "<div class='info-value'>" + String(_wifiManager->isConnected() ? "Connected" : "Disconnected") + "</div>";
        html += "</div>";

        if (_wifiManager->isConnected())
        {
            html += "<div class='info-item'>";
            html += "<div class='info-label'>Network:</div>";
            html += "<div class='info-value'>" + _wifiManager->getSSID() + "</div>";
            html += "</div>";
            html += "<div class='info-item'>";
            html += "<div class='info-label'>IP Address:</div>";
            html += "<div class='info-value'>" + _wifiManager->getIP().toString() + "</div>";
            html += "</div>";
        }
        html += "</div>";

        // Device Information
        if (_deviceManager)
        {
            html += "<h2>3-Relay Device Information</h2>";
            html += "<div class='info-grid'>";
            html += "<div class='info-item'>";
            html += "<div class='info-label'>Device ID:</div>";
            html += "<div class='info-value'>" + _deviceManager->getDeviceID() + "</div>";
            html += "</div>";
            html += "<div class='info-item'>";
            html += "<div class='info-label'>Device Name:</div>";
            html += "<div class='info-value'>" + _deviceManager->getDeviceName() + "</div>";
            html += "</div>";
            html += "<div class='info-item'>";
            html += "<div class='info-label'>Device Type:</div>";
            html += "<div class='info-value'>" + _deviceManager->getDeviceType() + "</div>";
            html += "</div>";
            html += "<div class='info-item'>";
            html += "<div class='info-label'>Switch Count:</div>";
            html += "<div class='info-value'>" + String(_deviceManager->getSwitchCount()) + "</div>";
            html += "</div>";
            html += "</div>";

            // Switch Status
            html += "<h2>3-Relay Switch Status</h2>";
            for (uint8_t i = 0; i < _deviceManager->getSwitchCount(); i++)
            {
                bool state = _deviceManager->getSwitchState(i);
                String switchClass = state ? "on" : "off";

                html += "<div class='switch-container'>";
                html += "<p><strong>Switch " + String(i + 1) + ":</strong>";
                html += "<span class='switch-state " + switchClass + "'>" + (state ? "ON" : "OFF") + "</span></p>";
                html += "</div>";
            }
        }

        // Navigation
        html += "<h2>Navigation</h2>";
        html += "<div class='button-container'>";
        if (_deviceManager)
        {
            html += "<button type='button' class='nav-button' onclick='window.location=\"/device\"'>Device Control</button>";
        }
        html += "</div>";

        html += _htmlFooter;
        _server.send(200, "text/html", html);
    }

    // Handle device control page (simplified for 3-Relay)
    void handleDevice()
    {
        if (!_deviceManager)
        {
            _server.send(404, "text/plain", "Device manager not initialized");
            return;
        }

        String html = _htmlHeader;
        html += "<h1>3-Relay Device Control</h1>";

        // Back button
        html += "<div class='button-container'>";
        html += "<button type='button' class='nav-button' onclick='window.location=\"/\"'>← Back to Home</button>";
        html += "</div>";

        // Switch controls (simplified for 3 switches)
        html += "<h2>3-Relay Switch Controls</h2>";
        for (uint8_t i = 0; i < _deviceManager->getSwitchCount(); i++)
        {
            bool state = _deviceManager->getSwitchState(i);
            String switchClass = state ? "on" : "off";

            html += "<div class='switch-container'>";
            html += "<p><strong>Switch " + String(i + 1) + ":</strong> ";
            html += "<span class='switch-state " + switchClass + "'>" + (state ? "ON" : "OFF") + "</span></p>";
            if (_touchManager)
            {
                html += "<p><strong>Touch Pin:</strong> " + String(_touchManager->getTouchPin(i)) + "</p>";
            }

            html += "<div class='button-container'>";
            html += "<button type='button' onclick='window.location=\"/switch?index=" + String(i) + "&state=" + (state ? "0" : "1") + "\"'>";
            html += (state ? "Turn OFF" : "Turn ON");
            html += "</button>";
            html += "</div>";
            html += "</div>";
        }

        html += _htmlFooter;
        _server.send(200, "text/html", html);
    }

    // Handle switch control
    void handleSwitch()
    {
        if (!_deviceManager)
        {
            _server.send(404, "text/plain", "Device manager not initialized");
            return;
        }

        if (!_server.hasArg("index") || !_server.hasArg("state"))
        {
            _server.send(400, "text/plain", "Missing parameters");
            return;
        }

        uint8_t index = _server.arg("index").toInt();
        bool state = _server.arg("state") == "1";

        if (index >= _deviceManager->getSwitchCount())
        {
            _server.send(400, "text/plain", "Invalid switch index");
            return;
        }

        _deviceManager->setSwitchState(index, state);

        // Redirect back to device page
        _server.sendHeader("Location", "/device", true);
        _server.send(302, "text/plain", "");
    }

    // Handle RGB OFF color control
    void handleRGBOff()
    {
        _server.send(200, "text/plain", "3-Relay RGB OFF control (simplified)");
    }

    // Handle RGB ON color control
    void handleRGBOn()
    {
        _server.send(200, "text/plain", "3-Relay RGB ON control (simplified)");
    }

    // Handle 404 errors
    void handle404()
    {
        String html = _htmlHeader;
        html += "<h1>3-Relay Page Not Found</h1>";
        html += "<p>The requested page was not found.</p>";
        html += "<button onclick='window.location=\"/\"'>Back to Home</button>";
        html += _htmlFooter;
        _server.send(404, "text/html", html);
    }
};

#endif // WEB_SERVER_MANAGER_H
