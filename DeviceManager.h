#ifndef DEVICE_MANAGER_H
#define DEVICE_MANAGER_H

#include <Arduino.h>
#include <EEPROM.h>

// EEPROM layout for device properties
// We'll start after the WiFi credentials
#define DEVICE_ID_ADDR 100
#define DEVICE_ID_SIZE 16
#define DEVICE_NAME_ADDR (DEVICE_ID_ADDR + DEVICE_ID_SIZE)
#define DEVICE_NAME_SIZE 32
#define DEVICE_TYPE_ADDR (DEVICE_NAME_ADDR + DEVICE_NAME_SIZE)
#define DEVICE_TYPE_SIZE 16
#define SWITCH_COUNT_ADDR (DEVICE_TYPE_ADDR + DEVICE_TYPE_SIZE)
#define SWITCH_STATE_ADDR (SWITCH_COUNT_ADDR + 1)
#define SWITCH_PINS_ADDR (SWITCH_STATE_ADDR + MAX_SWITCHES)
#define MAX_SWITCHES 2 // Maximum number of switches/relays supported

// Default GPIO pins for relays
const uint8_t DEFAULT_RELAY_PINS[MAX_SWITCHES] = {12, 13};

class DeviceManager
{
private:
    String _deviceID;
    String _deviceName;
    String _deviceType;
    uint8_t _switchCount;
    bool _switchState[MAX_SWITCHES];
    uint8_t _relayPins[MAX_SWITCHES];

    // Read string from EEPROM at specified address
    String readStringFromEEPROM(int startAddr, int maxLength)
    {
        char data[maxLength + 1];
        for (int i = 0; i < maxLength; i++)
        {
            data[i] = char(EEPROM.read(startAddr + i));
        }
        data[maxLength] = '\0';
        return String(data);
    }

    // Write string to EEPROM at specified address
    void writeStringToEEPROM(int startAddr, const String &strToWrite, int maxLength)
    {
        for (int i = 0; i < maxLength; i++)
        {
            if (i < strToWrite.length())
            {
                EEPROM.write(startAddr + i, strToWrite[i]);
            }
            else
            {
                EEPROM.write(startAddr + i, 0);
            }
        }
        EEPROM.commit();
    }

    // Generate a unique device ID if none exists
    String generateDeviceID()
    {
        // Use ESP32's MAC address as a base for the device ID
        uint8_t mac[6];
        WiFi.macAddress(mac);

        char deviceID[13];
        sprintf(deviceID, "%02X%02X%02X%02X%02X%02X", mac[0], mac[1], mac[2], mac[3], mac[4], mac[5]);

        return String(deviceID);
    }

public:
    DeviceManager(uint8_t switchCount = 2)
    {
        // Set default values
        _deviceType = "Switch";
        _switchCount = (switchCount > MAX_SWITCHES) ? MAX_SWITCHES : switchCount;

        // Initialize switch states to OFF and set default relay pins
        for (int i = 0; i < MAX_SWITCHES; i++)
        {
            _switchState[i] = false;
            _relayPins[i] = DEFAULT_RELAY_PINS[i];
        }
    }

    // Initialize device properties
    void begin()
    {
        // Check if device has been initialized before
        if (EEPROM.read(DEVICE_ID_ADDR) == 0xFF)
        {
            // First time initialization
            _deviceID = generateDeviceID();
            _deviceName = "Switch_" + _deviceID.substring(0, 6);

            // Save to EEPROM
            writeStringToEEPROM(DEVICE_ID_ADDR, _deviceID, DEVICE_ID_SIZE);
            writeStringToEEPROM(DEVICE_NAME_ADDR, _deviceName, DEVICE_NAME_SIZE);
            writeStringToEEPROM(DEVICE_TYPE_ADDR, _deviceType, DEVICE_TYPE_SIZE);
            EEPROM.write(SWITCH_COUNT_ADDR, _switchCount);

            // Initialize switch states and relay pins in EEPROM
            for (int i = 0; i < _switchCount; i++)
            {
                EEPROM.write(SWITCH_STATE_ADDR + i, 0);            // All switches OFF
                EEPROM.write(SWITCH_PINS_ADDR + i, _relayPins[i]); // Save default relay pins
            }

            EEPROM.commit();

            Serial.println("Device initialized for the first time");
            Serial.print("Device ID: ");
            Serial.println(_deviceID);
            Serial.print("Device Name: ");
            Serial.println(_deviceName);
            Serial.print("Device Type: ");
            Serial.println(_deviceType);
            Serial.print("Switch Count: ");
            Serial.println(_switchCount);
        }
        else
        {
            // Load existing configuration
            loadFromEEPROM();
        }

        // Initialize GPIO pins for relays
        for (int i = 0; i < _switchCount; i++)
        {
            pinMode(_relayPins[i], OUTPUT);
            // Set initial state (active low or active high depending on your relay)
            digitalWrite(_relayPins[i], _switchState[i] ? HIGH : LOW);

            Serial.print("Initialized relay pin ");
            Serial.print(_relayPins[i]);
            Serial.print(" to ");
            Serial.println(_switchState[i] ? "ON" : "OFF");
        }
    }

    // Load device properties from EEPROM
    void loadFromEEPROM()
    {
        _deviceID = readStringFromEEPROM(DEVICE_ID_ADDR, DEVICE_ID_SIZE);
        _deviceName = readStringFromEEPROM(DEVICE_NAME_ADDR, DEVICE_NAME_SIZE);
        _deviceType = readStringFromEEPROM(DEVICE_TYPE_ADDR, DEVICE_TYPE_SIZE);
        _switchCount = EEPROM.read(SWITCH_COUNT_ADDR);

        // Ensure switch count is valid
        if (_switchCount > MAX_SWITCHES || _switchCount == 0)
        {
            _switchCount = 2; // Default to 2 switches if invalid
            EEPROM.write(SWITCH_COUNT_ADDR, _switchCount);
            EEPROM.commit();
        }

        // Load switch states and relay pins
        for (int i = 0; i < _switchCount; i++)
        {
            _switchState[i] = EEPROM.read(SWITCH_STATE_ADDR + i) == 1;

            // Load relay pin or use default if not found
            uint8_t pin = EEPROM.read(SWITCH_PINS_ADDR + i);
            if (pin == 0xFF || pin >= 20)
            { // Invalid pin
                pin = DEFAULT_RELAY_PINS[i];
                EEPROM.write(SWITCH_PINS_ADDR + i, pin);
                EEPROM.commit();
            }
            _relayPins[i] = pin;
        }

        Serial.println("Device configuration loaded from EEPROM");
        Serial.print("Device ID: ");
        Serial.println(_deviceID);
        Serial.print("Device Name: ");
        Serial.println(_deviceName);
        Serial.print("Device Type: ");
        Serial.println(_deviceType);
        Serial.print("Switch Count: ");
        Serial.println(_switchCount);
    }

    // Save device properties to EEPROM
    void saveToEEPROM()
    {
        writeStringToEEPROM(DEVICE_ID_ADDR, _deviceID, DEVICE_ID_SIZE);
        writeStringToEEPROM(DEVICE_NAME_ADDR, _deviceName, DEVICE_NAME_SIZE);
        writeStringToEEPROM(DEVICE_TYPE_ADDR, _deviceType, DEVICE_TYPE_SIZE);
        EEPROM.write(SWITCH_COUNT_ADDR, _switchCount);

        // Save switch states
        for (int i = 0; i < _switchCount; i++)
        {
            EEPROM.write(SWITCH_STATE_ADDR + i, _switchState[i] ? 1 : 0);
        }

        EEPROM.commit();
        Serial.println("Device configuration saved to EEPROM");
    }

    // Getters and setters
    String getDeviceID() const { return _deviceID; }

    String getDeviceName() const { return _deviceName; }
    void setDeviceName(const String &name)
    {
        _deviceName = name;
        saveToEEPROM();
    }

    String getDeviceType() const { return _deviceType; }

    uint8_t getSwitchCount() const { return _switchCount; }

    bool getSwitchState(uint8_t switchIndex) const
    {
        if (switchIndex < _switchCount)
        {
            return _switchState[switchIndex];
        }
        return false;
    }

    void setSwitchState(uint8_t switchIndex, bool state)
    {
        if (switchIndex < _switchCount)
        {
            _switchState[switchIndex] = state;

            // Update EEPROM
            EEPROM.write(SWITCH_STATE_ADDR + switchIndex, state ? 1 : 0);
            EEPROM.commit();

            // Set the physical relay state
            digitalWrite(_relayPins[switchIndex], state ? HIGH : LOW);

            Serial.print("Switch ");
            Serial.print(switchIndex);
            Serial.print(" set to ");
            Serial.println(state ? "ON" : "OFF");
        }
    }

    void toggleSwitch(uint8_t switchIndex)
    {
        if (switchIndex < _switchCount)
        {
            setSwitchState(switchIndex, !_switchState[switchIndex]);
        }
    }
};

#endif // DEVICE_MANAGER_H
