#ifndef DEVICE_MANAGER_H
#define DEVICE_MANAGER_H

#include <Arduino.h>
#include <EEPROM.h>
#include "ShiftRegisterManager.h"

// EEPROM layout for device properties
// We'll start after the WiFi credentials
#define DEVICE_ID_ADDR 100
#define DEVICE_ID_SIZE 16
#define DEVICE_NAME_ADDR (DEVICE_ID_ADDR + DEVICE_ID_SIZE)
#define DEVICE_NAME_SIZE 32
#define DEVICE_TYPE_ADDR (DEVICE_NAME_ADDR + DEVICE_NAME_SIZE)
#define DEVICE_TYPE_SIZE 16
#define SWITCH_COUNT_ADDR (DEVICE_TYPE_ADDR + DEVICE_TYPE_SIZE)
#define SWITCH_STATE_ADDR (SWITCH_COUNT_ADDR + 1)
#define RGB_STATE_ADDR (SWITCH_STATE_ADDR + MAX_SWITCHES)
#define MAX_SWITCHES 4 // Maximum number of switches/relays supported

// Shift register control pins for ESP8266
const uint8_t SR_DATA_PIN = 13;  // D7 - Serial data
const uint8_t SR_CLOCK_PIN = 14; // D5 - Shift register clock
const uint8_t SR_LATCH_PIN = 12; // D6 - Storage register clock

class DeviceManager
{
private:
    String _deviceID;
    String _deviceName;
    String _deviceType;
    uint8_t _switchCount;
    bool _switchState[MAX_SWITCHES];
    uint8_t _rgbState[MAX_SWITCHES][3]; // RGB state for each switch [r,g,b]
    ShiftRegisterManager _shiftRegister;

    // Read string from EEPROM at specified address
    String readStringFromEEPROM(int startAddr, int maxLength)
    {
        char data[maxLength + 1];
        for (int i = 0; i < maxLength; i++)
        {
            data[i] = char(EEPROM.read(startAddr + i));
        }
        data[maxLength] = '\0';
        return String(data);
    }

    // Write string to EEPROM at specified address
    void writeStringToEEPROM(int startAddr, const String &strToWrite, int maxLength)
    {
        for (int i = 0; i < maxLength; i++)
        {
            if (i < strToWrite.length())
            {
                EEPROM.write(startAddr + i, strToWrite[i]);
            }
            else
            {
                EEPROM.write(startAddr + i, 0);
            }
        }
        EEPROM.commit();
    }

    // Generate a unique device ID if none exists
    String generateDeviceID()
    {
        // Use ESP32's MAC address as a base for the device ID
        uint8_t mac[6];
        WiFi.macAddress(mac);

        char deviceID[13];
        sprintf(deviceID, "%02X%02X%02X%02X%02X%02X", mac[0], mac[1], mac[2], mac[3], mac[4], mac[5]);

        return String(deviceID);
    }

public:
    DeviceManager(uint8_t switchCount = 4)
        : _shiftRegister(SR_DATA_PIN, SR_CLOCK_PIN, SR_LATCH_PIN)
    {
        // Set default values
        _deviceType = "Switch";
        _switchCount = (switchCount > MAX_SWITCHES) ? MAX_SWITCHES : switchCount;

        // Initialize switch states to OFF and RGB states to OFF
        for (int i = 0; i < MAX_SWITCHES; i++)
        {
            _switchState[i] = false;
            _rgbState[i][0] = 0; // Red
            _rgbState[i][1] = 0; // Green
            _rgbState[i][2] = 0; // Blue
        }
    }

    // Initialize device properties
    void begin()
    {
        // Initialize shift register first
        _shiftRegister.begin();

        // Check if device has been initialized before
        if (EEPROM.read(DEVICE_ID_ADDR) == 0xFF)
        {
            // First time initialization
            _deviceID = generateDeviceID();
            _deviceName = "Switch_" + _deviceID.substring(0, 6);

            // Save to EEPROM
            writeStringToEEPROM(DEVICE_ID_ADDR, _deviceID, DEVICE_ID_SIZE);
            writeStringToEEPROM(DEVICE_NAME_ADDR, _deviceName, DEVICE_NAME_SIZE);
            writeStringToEEPROM(DEVICE_TYPE_ADDR, _deviceType, DEVICE_TYPE_SIZE);
            EEPROM.write(SWITCH_COUNT_ADDR, _switchCount);

            // Initialize switch states and RGB states in EEPROM
            for (int i = 0; i < _switchCount; i++)
            {
                EEPROM.write(SWITCH_STATE_ADDR + i, 0); // All switches OFF
                // Initialize RGB states to OFF
                for (int j = 0; j < 3; j++)
                {
                    EEPROM.write(RGB_STATE_ADDR + (i * 3) + j, 0);
                }
            }

            EEPROM.commit();

            Serial.println("Device initialized for the first time");
            Serial.print("Device ID: ");
            Serial.println(_deviceID);
            Serial.print("Device Name: ");
            Serial.println(_deviceName);
            Serial.print("Device Type: ");
            Serial.println(_deviceType);
            Serial.print("Switch Count: ");
            Serial.println(_switchCount);
        }
        else
        {
            // Load existing configuration
            loadFromEEPROM();
        }

        // Set initial states using shift register
        for (int i = 0; i < _switchCount; i++)
        {
            _shiftRegister.setRelay(i, _switchState[i]);
            _shiftRegister.setRGB(i, _rgbState[i][0], _rgbState[i][1], _rgbState[i][2]);

            Serial.print("Initialized switch ");
            Serial.print(i + 1);
            Serial.print(" to ");
            Serial.print(_switchState[i] ? "ON" : "OFF");
            Serial.print(" with RGB(");
            Serial.print(_rgbState[i][0]);
            Serial.print(",");
            Serial.print(_rgbState[i][1]);
            Serial.print(",");
            Serial.print(_rgbState[i][2]);
            Serial.println(")");
        }
    }

    // Load device properties from EEPROM
    void loadFromEEPROM()
    {
        _deviceID = readStringFromEEPROM(DEVICE_ID_ADDR, DEVICE_ID_SIZE);
        _deviceName = readStringFromEEPROM(DEVICE_NAME_ADDR, DEVICE_NAME_SIZE);
        _deviceType = readStringFromEEPROM(DEVICE_TYPE_ADDR, DEVICE_TYPE_SIZE);
        _switchCount = EEPROM.read(SWITCH_COUNT_ADDR);

        // Ensure switch count is valid
        if (_switchCount > MAX_SWITCHES || _switchCount == 0)
        {
            _switchCount = 4; // Default to 4 switches if invalid
            EEPROM.write(SWITCH_COUNT_ADDR, _switchCount);
            EEPROM.commit();
        }

        // Load switch states and RGB states
        for (int i = 0; i < _switchCount; i++)
        {
            _switchState[i] = EEPROM.read(SWITCH_STATE_ADDR + i) == 1;

            // Load RGB states or use default if not found
            for (int j = 0; j < 3; j++)
            {
                uint8_t rgbValue = EEPROM.read(RGB_STATE_ADDR + (i * 3) + j);
                if (rgbValue == 0xFF)
                { // Invalid RGB value
                    rgbValue = 0;
                    EEPROM.write(RGB_STATE_ADDR + (i * 3) + j, rgbValue);
                    EEPROM.commit();
                }
                _rgbState[i][j] = rgbValue;
            }
        }

        Serial.println("Device configuration loaded from EEPROM");
        Serial.print("Device ID: ");
        Serial.println(_deviceID);
        Serial.print("Device Name: ");
        Serial.println(_deviceName);
        Serial.print("Device Type: ");
        Serial.println(_deviceType);
        Serial.print("Switch Count: ");
        Serial.println(_switchCount);
    }

    // Save device properties to EEPROM
    void saveToEEPROM()
    {
        writeStringToEEPROM(DEVICE_ID_ADDR, _deviceID, DEVICE_ID_SIZE);
        writeStringToEEPROM(DEVICE_NAME_ADDR, _deviceName, DEVICE_NAME_SIZE);
        writeStringToEEPROM(DEVICE_TYPE_ADDR, _deviceType, DEVICE_TYPE_SIZE);
        EEPROM.write(SWITCH_COUNT_ADDR, _switchCount);

        // Save switch states and RGB states
        for (int i = 0; i < _switchCount; i++)
        {
            EEPROM.write(SWITCH_STATE_ADDR + i, _switchState[i] ? 1 : 0);
            // Save RGB states
            for (int j = 0; j < 3; j++)
            {
                EEPROM.write(RGB_STATE_ADDR + (i * 3) + j, _rgbState[i][j]);
            }
        }

        EEPROM.commit();
        Serial.println("Device configuration saved to EEPROM");
    }

    // Getters and setters
    String getDeviceID() const { return _deviceID; }

    String getDeviceName() const { return _deviceName; }
    void setDeviceName(const String &name)
    {
        _deviceName = name;
        saveToEEPROM();
    }

    String getDeviceType() const { return _deviceType; }

    uint8_t getSwitchCount() const { return _switchCount; }

    bool getSwitchState(uint8_t switchIndex) const
    {
        if (switchIndex < _switchCount)
        {
            return _switchState[switchIndex];
        }
        return false;
    }

    void setSwitchState(uint8_t switchIndex, bool state)
    {
        if (switchIndex < _switchCount)
        {
            _switchState[switchIndex] = state;

            // Update EEPROM
            EEPROM.write(SWITCH_STATE_ADDR + switchIndex, state ? 1 : 0);
            EEPROM.commit();

            // Set the physical relay state using shift register
            _shiftRegister.setRelay(switchIndex, state);

            Serial.print("Switch ");
            Serial.print(switchIndex + 1);
            Serial.print(" set to ");
            Serial.println(state ? "ON" : "OFF");
        }
    }

    void toggleSwitch(uint8_t switchIndex)
    {
        if (switchIndex < _switchCount)
        {
            setSwitchState(switchIndex, !_switchState[switchIndex]);
        }
    }

    // RGB LED control methods
    void setRGB(uint8_t switchIndex, uint8_t r, uint8_t g, uint8_t b)
    {
        if (switchIndex < _switchCount)
        {
            _rgbState[switchIndex][0] = r;
            _rgbState[switchIndex][1] = g;
            _rgbState[switchIndex][2] = b;

            // Update EEPROM
            for (int j = 0; j < 3; j++)
            {
                EEPROM.write(RGB_STATE_ADDR + (switchIndex * 3) + j, _rgbState[switchIndex][j]);
            }
            EEPROM.commit();

            // Set the physical RGB state using shift register
            _shiftRegister.setRGB(switchIndex, r, g, b);

            Serial.print("RGB for Switch ");
            Serial.print(switchIndex + 1);
            Serial.print(" set to R:");
            Serial.print(r);
            Serial.print(" G:");
            Serial.print(g);
            Serial.print(" B:");
            Serial.println(b);
        }
    }

    void getRGB(uint8_t switchIndex, uint8_t &r, uint8_t &g, uint8_t &b)
    {
        if (switchIndex < _switchCount)
        {
            r = _rgbState[switchIndex][0];
            g = _rgbState[switchIndex][1];
            b = _rgbState[switchIndex][2];
        }
        else
        {
            r = g = b = 0;
        }
    }

    // Get shift register manager for advanced control
    ShiftRegisterManager *getShiftRegister()
    {
        return &_shiftRegister;
    }
};

#endif // DEVICE_MANAGER_H
