#ifndef WEB_SERVER_MANAGER_H
#define WEB_SERVER_MANAGER_H

#include <Arduino.h>
#include <ESP8266WiFi.h>
#include <ESP8266WebServer.h>
#include "WiFiManager.h"
#include "DeviceManager.h"
#include "TouchSensorManager.h"

class WebServerManager
{
private:
    ESP8266WebServer _server;
    WiFiManager *_wifiManager;
    DeviceManager *_deviceManager;
    TouchSensorManager *_touchManager;
    bool _serverRunning;
    int _port;

    // HTML templates
    const char *_htmlHeader = R"(
<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ESP8266 Device Control</title>
    <script>
    function togglePassword(id) {
      var input = document.getElementById(id);
      var checkbox = document.getElementById('show_' + id);
      if (checkbox.checked) {
        input.type = 'text';
      } else {
        input.type = 'password';
      }
    }
    </script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            max-width: 800px;
            margin: 0 auto;
            background-color: #121212;
            color: #e0e0e0;
        }
        h1, h2, h3 { color: #ffffff; }
        .container { margin-top: 20px; }
        .network-list { list-style: none; padding: 0; }
        .network-item {
            padding: 12px;
            margin-bottom: 8px;
            border: 1px solid #333;
            border-radius: 10px;
            cursor: pointer;
            background-color: #1e1e1e;
        }
        .network-item:hover { background-color: #2c2c2c; }
        .hidden-network-item { background-color: #1a2e1a; }
        .signal-strength { float: right; color: #aaaaaa; }
        .form-group { margin-bottom: 15px; position: relative; }
        label { display: block; margin-bottom: 5px; color: #e0e0e0; }
        input[type="text"], input[type="password"] {
            width: 100%;
            padding: 10px;
            box-sizing: border-box;
            border-radius: 8px;
            border: 1px solid #444;
            background-color: #2c2c2c;
            color: #e0e0e0;
        }
        button {
            background-color: #388e3c;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            min-width: 100px;
        }
        button:hover { background-color: #2e7d32; }
        .message { padding: 10px; margin-bottom: 10px; border-radius: 8px; }
        .success { background-color: #1b5e20; color: #e0e0e0; }
        .error { background-color: #b71c1c; color: #e0e0e0; }
        .hidden-network { margin-top: 20px; }
        .back-button { background-color: #455a64; margin-right: 10px; }
        .back-button:hover { background-color: #37474f; }
        .button-container { display: flex; margin-top: 15px; }
        .scan-button { margin-bottom: 20px; }
        .scan-item { background-color: #388e3c; color: white; cursor: pointer; }
        .scan-item:hover { background-color: #2e7d32; }
        .plus-sign { color: #4CAF50; font-weight: bold; font-size: 1.2em; margin-right: 5px; }
        .password-toggle { display: flex; align-items: center; margin-top: 5px; }
        .password-toggle label { margin-left: 5px; margin-bottom: 0; cursor: pointer; color: #e0e0e0; }

        /* Navigation menu */
        .nav-menu {
            display: flex;
            background-color: #1e1e1e;
            border-radius: 10px;
            margin-bottom: 20px;
            overflow: hidden;
            border: 1px solid #333;
        }
        .nav-item {
            padding: 12px 15px;
            text-align: center;
            flex-grow: 1;
            cursor: pointer;
            border-right: 1px solid #333;
            transition: background-color 0.3s;
            color: #e0e0e0;
        }
        .nav-item:last-child {
            border-right: none;
        }
        .nav-item:hover {
            background-color: #2c2c2c;
        }
        .nav-item.active {
            background-color: #1976D2;
            color: white;
        }

        /* Status indicators */
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-connected {
            background-color: #4CAF50;
        }
        .status-disconnected {
            background-color: #f44336;
        }

        /* Switch styling */
        .switch-state {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 5px;
            font-weight: bold;
        }
        .switch-state.on {
            background-color: #388e3c;
            color: white;
        }
        .switch-state.off {
            background-color: #d32f2f;
            color: white;
        }

        /* Loading spinner */
        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: white;
            animation: spin 1s ease-in-out infinite;
            vertical-align: middle;
            margin-left: 8px;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* Info cards */
        .info-card {
            border: 1px solid #333;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
            background-color: #1e1e1e;
        }
        .info-card h3 {
            margin-top: 0;
            color: #ffffff;
            border-bottom: 1px solid #333;
            padding-bottom: 8px;
            margin-bottom: 12px;
        }
        .section-header {
            font-size: 1.2em;
            font-weight: bold;
            margin-top: 20px;
            margin-bottom: 10px;
            color: #ffffff;
        }
        .current-network {
            background-color: #1a2e1a;
            border-left: 4px solid #388e3c;
        }
    </style>
</head>
<body>
    <h1>ESP8266 Device Control</h1>
    <div class="nav-menu">
        <div class="nav-item" onclick="window.location='/'">Dashboard</div>
        <div class="nav-item" onclick="window.location='/wifi'">WiFi Setup</div>
        <div class="nav-item" onclick="window.location='/device'">Device Control</div>
        <div class="nav-item" onclick="window.location='/mqtt'">MQTT Status</div>
    </div>
    <div class="container">
)";

    const char *_htmlFooter = R"(
    </div>
    <script>
        function setRGBOff(switchIndex) {
            var r = document.getElementById('rOff' + switchIndex).checked ? 1 : 0;
            var g = document.getElementById('gOff' + switchIndex).checked ? 1 : 0;
            var b = document.getElementById('bOff' + switchIndex).checked ? 1 : 0;

            var xhr = new XMLHttpRequest();
            xhr.open('POST', '/rgb/off', true);
            xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
            xhr.send('index=' + switchIndex + '&r=' + r + '&g=' + g + '&b=' + b);
        }

        function setRGBOn(switchIndex) {
            var r = document.getElementById('rOn' + switchIndex).checked ? 1 : 0;
            var g = document.getElementById('gOn' + switchIndex).checked ? 1 : 0;
            var b = document.getElementById('bOn' + switchIndex).checked ? 1 : 0;

            var xhr = new XMLHttpRequest();
            xhr.open('POST', '/rgb/on', true);
            xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
            xhr.send('index=' + switchIndex + '&r=' + r + '&g=' + g + '&b=' + b);
        }
    </script>
</body>
</html>
)";

public:
    WebServerManager(WiFiManager *wifiManager, DeviceManager *deviceManager = nullptr, TouchSensorManager *touchManager = nullptr, int port = 80)
        : _server(port), _wifiManager(wifiManager), _deviceManager(deviceManager), _touchManager(touchManager), _serverRunning(false), _port(port)
    {
    }

    // Start the web server
    void begin()
    {
        if (_serverRunning)
        {
            return;
        }

        // Set up routes
        _server.on("/", [this]()
                   { handleRoot(); });

        // WiFi configuration routes
        _server.on("/wifi", [this]()
                   { handleWifi(); });
        _server.on("/connect", [this]()
                   { handleConnect(); });
        _server.on("/hidden", [this]()
                   { handleHiddenNetwork(); });
        _server.on("/submit-hidden", [this]()
                   { handleSubmitHidden(); });
        _server.on("/success", [this]()
                   { handleSuccess(); });

        // Device control routes
        _server.on("/device", [this]()
                   { handleDevice(); });
        _server.on("/switch", [this]()
                   { handleSwitch(); });
        _server.on("/rgb/off", [this]()
                   { handleRGBOff(); });
        _server.on("/rgb/on", [this]()
                   { handleRGBOn(); });

        // MQTT status route
        _server.on("/mqtt", [this]()
                   { handleMqtt(); });

        // Start server
        _server.begin();
        _serverRunning = true;
        Serial.print("Web server started on port ");
        Serial.println(_port);
        Serial.print("Connect to http://");
        Serial.print(WiFi.softAPIP());
        Serial.println("/");
    }

    // Stop the web server
    void stop()
    {
        if (!_serverRunning)
        {
            return;
        }

        _server.stop();
        _serverRunning = false;
        Serial.println("Web server stopped");
    }

    // Handle client requests
    void handleClient()
    {
        if (_serverRunning)
        {
            _server.handleClient();
        }
    }

    // Check if server is running
    bool isRunning()
    {
        return _serverRunning;
    }

private:
    // Handle root page - show dashboard with device information
    void handleRoot()
    {
        String html = _htmlHeader;

        // Highlight the active nav item
        html += "<script>document.getElementsByClassName('nav-item')[0].classList.add('active');</script>";

        html += "<h2>Device Dashboard</h2>";

        // Device Information Card
        html += "<div class='info-card'>";
        html += "<h3>Device Information</h3>";

        if (_deviceManager)
        {
            html += "<p><strong>Device ID:</strong> " + _deviceManager->getDeviceID() + "</p>";
            html += "<p><strong>Device Name:</strong> " + _deviceManager->getDeviceName() + "</p>";
            html += "<p><strong>Device Type:</strong> " + _deviceManager->getDeviceType() + "</p>";
            html += "<p><strong>Switch Count:</strong> " + String(_deviceManager->getSwitchCount()) + "</p>";
        }
        else
        {
            html += "<p>Device information not available</p>";
        }
        html += "</div>";

        // WiFi Status Card
        html += "<div class='info-card'>";
        html += "<h3>WiFi Status</h3>";

        if (_wifiManager->isConnected())
        {
            String currentSSID = WiFi.SSID();
            if (currentSSID.length() == 0)
            {
                currentSSID = _wifiManager->getSSID();
            }
            IPAddress ip = WiFi.localIP();

            html += "<p><span class='status-indicator status-connected'></span> <strong>Connected</strong></p>";
            html += "<p><strong>Network:</strong> " + currentSSID + "</p>";
            html += "<p><strong>IP Address:</strong> " + ip.toString() + "</p>";
            html += "<p><strong>Signal Strength:</strong> " + String(WiFi.RSSI()) + " dBm</p>";
        }
        else
        {
            html += "<p><span class='status-indicator status-disconnected'></span> <strong>Disconnected</strong></p>";
            html += "<p>Not connected to any WiFi network</p>";
        }

        html += "<div class='button-container'>";
        html += "<button type='button' onclick='window.location=\"/wifi\"'>WiFi Settings</button>";
        html += "</div>";
        html += "</div>";

        // Switch Status Card
        if (_deviceManager)
        {
            html += "<div class='info-card'>";
            html += "<h3>Switch Status</h3>";

            for (uint8_t i = 0; i < _deviceManager->getSwitchCount(); i++)
            {
                bool state = _deviceManager->getSwitchState(i);
                String switchClass = state ? "on" : "off";

                html += "<p><strong>Switch " + String(i + 1) + ":</strong> ";
                html += "<span class='switch-state " + switchClass + "'>" + (state ? "ON" : "OFF") + "</span></p>";
            }

            html += "<div class='button-container'>";
            html += "<button type='button' onclick='window.location=\"/device\"'>Control Switches</button>";
            html += "</div>";
            html += "</div>";
        }

        html += _htmlFooter;
        _server.send(200, "text/html", html);
    }

    // Handle WiFi configuration page
    void handleWifi()
    {
        String html = _htmlHeader;

        // Highlight the active nav item
        html += "<script>document.getElementsByClassName('nav-item')[1].classList.add('active');</script>";

        html += "<h2>WiFi Configuration</h2>";

        // Add a scan button at the top with loading indicator
        html += "<div class='scan-button'>";
        html += "<div class='network-item scan-item' id='scanBtn' onclick='startScan()'>";
        html += "<span id='scanText'>Scan for Networks</span>";
        html += "<span id='loadingIndicator' class='loading-spinner' style='display:none; float: right;'></span>";
        html += "</div>";
        html += "</div>";

        // Add JavaScript for scan button loading effect
        html += "<script>";
        html += "function startScan() {";
        html += "  document.getElementById('loadingIndicator').style.display = 'inline-block';";
        html += "  setTimeout(function() { window.location.reload(); }, 300);";
        html += "}";
        html += "</script>";

        // Show current connection if connected
        if (_wifiManager->isConnected())
        {
            html += "<div class='section-header'>Current Connection</div>";
            html += "<ul class='network-list'>";

            // Get the current SSID directly from WiFi library if connected
            String currentSSID = WiFi.SSID();
            // If empty, fall back to the stored SSID in WiFiManager
            if (currentSSID.length() == 0)
            {
                currentSSID = _wifiManager->getSSID();
            }
            IPAddress ip = WiFi.localIP();

            html += "<li class='network-item current-network'>";
            html += currentSSID;
            html += "<span class='signal-strength'>Connected (" + ip.toString() + ")</span>";
            html += "</li>";

            html += "</ul>";
        }

        // Scan for networks
        Serial.println("Scanning for networks from web interface...");
        int numNetworks = _wifiManager->scanNetworks();

        // Add section header for available networks
        html += "<div class='section-header'>Available Networks</div>";

        if (numNetworks == 0)
        {
            html += "<p>No networks found. Please use the Scan button to try again.</p>";
        }
        else
        {
            html += "<ul class='network-list'>";

            // Get the current connected SSID to filter it out from the available list
            String currentConnectedSSID = "";
            if (_wifiManager->isConnected())
            {
                currentConnectedSSID = WiFi.SSID();
                if (currentConnectedSSID.length() == 0)
                {
                    currentConnectedSSID = _wifiManager->getSSID();
                }
            }

            for (int i = 0; i < numNetworks; i++)
            {
                String ssid = _wifiManager->getSSID(i);

                // Skip the current connected network in the available networks list
                if (ssid == currentConnectedSSID)
                {
                    continue;
                }

                int rssi = _wifiManager->getRSSI(i);
                String encType = _wifiManager->getEncryptionType(i);

                // Calculate signal strength icon
                String signalStrength;
                if (rssi > -50)
                {
                    signalStrength = "Strong";
                }
                else if (rssi > -70)
                {
                    signalStrength = "Good";
                }
                else if (rssi > -80)
                {
                    signalStrength = "Fair";
                }
                else
                {
                    signalStrength = "Weak";
                }

                html += "<li class='network-item' onclick='window.location=\"/connect?ssid=" + urlEncode(ssid) + "\"'>";
                html += ssid;
                html += "<span class='signal-strength'>" + signalStrength + " (" + String(rssi) + " dBm)</span>";
                html += "</li>";
            }

            html += "</ul>";
        }

        html += "<div class='hidden-network'>";
        html += "<div class='network-item hidden-network-item' onclick='window.location=\"/hidden\"'>";
        html += "<span class='plus-sign'>&#43;</span>Add Hidden Network";
        html += "</div>";
        html += "</div>";

        html += _htmlFooter;

        _server.send(200, "text/html", html);
    }

    // Handle connect page - show password form for selected network
    void handleConnect()
    {
        String ssid = _server.arg("ssid");

        String html = _htmlHeader;
        html += "<h2>Connect to " + ssid + "</h2>";
        html += "<form method='post' action='/submit-hidden'>";
        html += "<input type='hidden' name='ssid' value='" + ssid + "'>";

        html += "<div class='form-group'>";
        html += "<label for='password'>Password:</label>";
        html += "<input type='password' id='password' name='password' required>";
        html += "</div>";
        html += "<div class='password-toggle'>";
        html += "<input type='checkbox' id='show_password' onchange='togglePassword(\"password\")'>";
        html += "<label for='show_password'>Show password</label>";
        html += "</div>";

        html += "<div class='button-container'>";
        html += "<button type='button' class='back-button' onclick='window.location=\"/wifi\"'>Back</button>";
        html += "<button type='submit'>Connect</button>";
        html += "</div>";
        html += "</form>";
        html += _htmlFooter;

        _server.send(200, "text/html", html);
    }

    // Handle hidden network page - show form for hidden network
    void handleHiddenNetwork()
    {
        String html = _htmlHeader;
        html += "<h2>Connect to Hidden Network</h2>";
        html += "<form method='post' action='/submit-hidden'>";

        html += "<div class='form-group'>";
        html += "<label for='ssid'>Network Name (SSID):</label>";
        html += "<input type='text' id='ssid' name='ssid' required>";
        html += "</div>";

        html += "<div class='form-group'>";
        html += "<label for='password'>Password:</label>";
        html += "<input type='password' id='password' name='password' required>";
        html += "</div>";
        html += "<div class='password-toggle'>";
        html += "<input type='checkbox' id='show_password' onchange='togglePassword(\"password\")'>";
        html += "<label for='show_password'>Show password</label>";
        html += "</div>";

        html += "<div class='button-container'>";
        html += "<button type='button' class='back-button' onclick='window.location=\"/wifi\"'>Back</button>";
        html += "<button type='submit'>Connect</button>";
        html += "</div>";
        html += "</form>";
        html += _htmlFooter;

        _server.send(200, "text/html", html);
    }

    // Handle form submission for both regular and hidden networks
    void handleSubmitHidden()
    {
        String ssid = _server.arg("ssid");
        String password = _server.arg("password");

        if (ssid.length() == 0)
        {
            _server.send(400, "text/plain", "SSID is required");
            return;
        }

        // Attempt to connect immediately - don't save credentials yet
        bool connectionSuccess = _wifiManager->connect(ssid, password);

        if (connectionSuccess)
        {
            Serial.println("Connection successful - saving credentials");
            // Only save credentials if connection was successful
            _wifiManager->saveCredentials(ssid, password);

            // If connection was successful, redirect to the dashboard
            _server.sendHeader("Location", "/", true);
            _server.send(302, "text/plain", "");
        }
        else
        {
            Serial.println("Connection failed - NOT saving credentials");
            // If connection failed, show the error message on the success page
            _server.sendHeader("Location", "/success", true);
            _server.send(302, "text/plain", "");
        }
    }

    // Handle success page - now only used for connection failures
    void handleSuccess()
    {
        String html = _htmlHeader;

        // We only show the error message now since successful connections go directly to the main page
        html += "<div class='message error'>";
        html += "<h2>Connection Failed</h2>";
        html += "<p>The device could not connect to the network after multiple attempts.</p>";
        html += "<p>Please check your password and make sure the network is in range.</p>";
        html += "<p>You can try again by selecting the network from the list.</p>";
        html += "</div>";

        html += "<div class='button-container' style='margin-top: 20px;'>";
        html += "<button type='button' onclick='window.location=\"/wifi\"'>Back to Networks</button>";
        html += "</div>";
        html += _htmlFooter;

        _server.send(200, "text/html", html);
    }

    // Handle device page - show device information and controls
    void handleDevice()
    {
        if (!_deviceManager)
        {
            _server.send(404, "text/plain", "Device manager not initialized");
            return;
        }

        String html = _htmlHeader;

        // Highlight the active nav item
        html += "<script>document.getElementsByClassName('nav-item')[2].classList.add('active');</script>";

        html += "<h2>Device Control</h2>";

        // Device Information Card
        html += "<div class='info-card'>";
        html += "<h3>Device Information</h3>";
        html += "<p><strong>Device ID:</strong> " + _deviceManager->getDeviceID() + "</p>";
        html += "<p><strong>Device Name:</strong> " + _deviceManager->getDeviceName() + "</p>";
        html += "<p><strong>Device Type:</strong> " + _deviceManager->getDeviceType() + "</p>";
        html += "<p><strong>Switch Count:</strong> " + String(_deviceManager->getSwitchCount()) + "</p>";
        html += "</div>";

        // Switch Controls Card
        html += "<div class='info-card'>";
        html += "<h3>Switch Controls</h3>";

        // Add switch controls
        for (uint8_t i = 0; i < _deviceManager->getSwitchCount(); i++)
        {
            bool state = _deviceManager->getSwitchState(i);
            String switchClass = state ? "on" : "off";

            bool rOff, gOff, bOff, rOn, gOn, bOn;
            _deviceManager->getRGBOff(i, rOff, gOff, bOff);
            _deviceManager->getRGBOn(i, rOn, gOn, bOn);

            html += "<div class='switch-container' style='margin-bottom: 25px; border: 1px solid #555; padding: 15px; border-radius: 8px;'>";
            html += "<p><strong>Switch " + String(i + 1) + ":</strong> ";
            html += "<span class='switch-state " + switchClass + "'>" + (state ? "ON" : "OFF") + "</span></p>";
            if (_touchManager)
            {
                html += "<p><strong>Touch Pin:</strong> " + String(_touchManager->getTouchPin(i)) + "</p>";
            }

            html += "<div class='button-container' style='margin-bottom: 15px;'>";
            html += "<button type='button' onclick='window.location=\"/switch?index=" + String(i) + "&state=" + (state ? "0" : "1") + "\"'>";
            html += (state ? "Turn OFF" : "Turn ON");
            html += "</button>";
            html += "</div>";

            // RGB Controls - Digital ON/OFF only
            html += "<div style='margin-top: 15px;'>";
            html += "<p><strong>RGB LED Colors (Digital ON/OFF):</strong></p>";

            // OFF Color Controls
            html += "<div style='margin-bottom: 15px; padding: 10px; border: 1px solid #666; border-radius: 5px;'>";
            html += "<p><strong>OFF Color:</strong></p>";
            html += "<div style='display: flex; gap: 15px; align-items: center;'>";
            html += "<label><input type='checkbox' " + String(rOff ? "checked" : "") + " onchange='setRGBOff(" + String(i) + ")' id='rOff" + String(i) + "'> Red</label>";
            html += "<label><input type='checkbox' " + String(gOff ? "checked" : "") + " onchange='setRGBOff(" + String(i) + ")' id='gOff" + String(i) + "'> Green</label>";
            html += "<label><input type='checkbox' " + String(bOff ? "checked" : "") + " onchange='setRGBOff(" + String(i) + ")' id='bOff" + String(i) + "'> Blue</label>";
            html += "<div style='width: 30px; height: 20px; border: 1px solid #ccc; background-color: rgb(" + String(rOff ? 255 : 0) + "," + String(gOff ? 255 : 0) + "," + String(bOff ? 255 : 0) + ");'></div>";
            html += "</div>";
            html += "</div>";

            // ON Color Controls
            html += "<div style='margin-bottom: 15px; padding: 10px; border: 1px solid #666; border-radius: 5px;'>";
            html += "<p><strong>ON Color:</strong></p>";
            html += "<div style='display: flex; gap: 15px; align-items: center;'>";
            html += "<label><input type='checkbox' " + String(rOn ? "checked" : "") + " onchange='setRGBOn(" + String(i) + ")' id='rOn" + String(i) + "'> Red</label>";
            html += "<label><input type='checkbox' " + String(gOn ? "checked" : "") + " onchange='setRGBOn(" + String(i) + ")' id='gOn" + String(i) + "'> Green</label>";
            html += "<label><input type='checkbox' " + String(bOn ? "checked" : "") + " onchange='setRGBOn(" + String(i) + ")' id='bOn" + String(i) + "'> Blue</label>";
            html += "<div style='width: 30px; height: 20px; border: 1px solid #ccc; background-color: rgb(" + String(rOn ? 255 : 0) + "," + String(gOn ? 255 : 0) + "," + String(bOn ? 255 : 0) + ");'></div>";
            html += "</div>";
            html += "</div>";
            html += "</div>";
            html += "</div>";
        }

        html += "</div>";

        html += _htmlFooter;
        _server.send(200, "text/html", html);
    }

    // Handle MQTT status page
    void handleMqtt()
    {
        String html = _htmlHeader;

        // Highlight the active nav item
        html += "<script>document.getElementsByClassName('nav-item')[3].classList.add('active');</script>";

        html += "<h2>MQTT Status</h2>";

        // MQTT Status Card
        html += "<div class='info-card'>";
        html += "<h3>MQTT Connection</h3>";

        // We don't have direct access to MQTT status, so we'll just show a placeholder
        // In a real implementation, you would need to pass the MQTT manager to this class
        html += "<p><strong>Status:</strong> <span class='status-indicator status-disconnected'></span> Information not available</p>";
        html += "<p><em>MQTT status information will be implemented in a future update.</em></p>";

        html += "</div>";

        // MQTT Topics Card
        if (_deviceManager)
        {
            html += "<div class='info-card'>";
            html += "<h3>MQTT Topics</h3>";

            String deviceId = _deviceManager->getDeviceID();

            html += "<p><strong>Device ID:</strong> " + deviceId + "</p>";
            html += "<p><strong>Status Topic:</strong> homeassistant/" + deviceId + "/status</p>";

            for (uint8_t i = 0; i < _deviceManager->getSwitchCount(); i++)
            {
                html += "<p><strong>Switch " + String(i + 1) + " Topic:</strong> homeassistant/" + deviceId + "/switch/" + String(i) + "/state</p>";
            }

            html += "</div>";
        }

        html += _htmlFooter;
        _server.send(200, "text/html", html);
    }

    // Handle switch control
    void handleSwitch()
    {
        if (!_deviceManager)
        {
            _server.send(404, "text/plain", "Device manager not initialized");
            return;
        }

        if (!_server.hasArg("index") || !_server.hasArg("state"))
        {
            _server.send(400, "text/plain", "Missing parameters");
            return;
        }

        uint8_t index = _server.arg("index").toInt();
        bool state = _server.arg("state") == "1";

        if (index >= _deviceManager->getSwitchCount())
        {
            _server.send(400, "text/plain", "Invalid switch index");
            return;
        }

        _deviceManager->setSwitchState(index, state);

        // Redirect back to device page
        _server.sendHeader("Location", "/device", true);
        _server.send(302, "text/plain", "");
    }

    // Handle RGB OFF color control
    void handleRGBOff()
    {
        if (!_deviceManager)
        {
            _server.send(404, "text/plain", "Device manager not initialized");
            return;
        }

        if (!_server.hasArg("index") || !_server.hasArg("r") || !_server.hasArg("g") || !_server.hasArg("b"))
        {
            _server.send(400, "text/plain", "Missing parameters");
            return;
        }

        uint8_t index = _server.arg("index").toInt();
        bool r = _server.arg("r").toInt() > 0;
        bool g = _server.arg("g").toInt() > 0;
        bool b = _server.arg("b").toInt() > 0;

        if (index >= _deviceManager->getSwitchCount())
        {
            _server.send(400, "text/plain", "Invalid switch index");
            return;
        }

        _deviceManager->setRGBOff(index, r, g, b);

        _server.send(200, "text/plain", "RGB OFF color set successfully");
    }

    // Handle RGB ON color control
    void handleRGBOn()
    {
        if (!_deviceManager)
        {
            _server.send(404, "text/plain", "Device manager not initialized");
            return;
        }

        if (!_server.hasArg("index") || !_server.hasArg("r") || !_server.hasArg("g") || !_server.hasArg("b"))
        {
            _server.send(400, "text/plain", "Missing parameters");
            return;
        }

        uint8_t index = _server.arg("index").toInt();
        bool r = _server.arg("r").toInt() > 0;
        bool g = _server.arg("g").toInt() > 0;
        bool b = _server.arg("b").toInt() > 0;

        if (index >= _deviceManager->getSwitchCount())
        {
            _server.send(400, "text/plain", "Invalid switch index");
            return;
        }

        _deviceManager->setRGBOn(index, r, g, b);

        _server.send(200, "text/plain", "RGB ON color set successfully");
    }

    // URL encode a string
    String urlEncode(const String &str)
    {
        String encodedString = "";
        char c;
        char code0;
        char code1;

        for (int i = 0; i < str.length(); i++)
        {
            c = str.charAt(i);
            if (c == ' ')
            {
                encodedString += '+';
            }
            else if (isalnum(c))
            {
                encodedString += c;
            }
            else
            {
                code1 = (c & 0xf) + '0';
                if ((c & 0xf) > 9)
                {
                    code1 = (c & 0xf) - 10 + 'A';
                }
                c = (c >> 4) & 0xf;
                code0 = c + '0';
                if (c > 9)
                {
                    code0 = c - 10 + 'A';
                }
                encodedString += '%';
                encodedString += code0;
                encodedString += code1;
            }
        }
        return encodedString;
    }
};

#endif // WEB_SERVER_MANAGER_H
